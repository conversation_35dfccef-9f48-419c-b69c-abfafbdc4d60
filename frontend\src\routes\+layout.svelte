<script lang="ts">
	import '../app.css'
	import { onMount } from 'svelte'
	import { browser } from '$app/environment'
	import { waitLocale, locale } from '$lib/i18n'
	import { getCurrentLocale, isRTL } from '$lib/i18n'
	import { authStore } from '$lib/stores/auth'
	import { cartActions } from '$lib/stores/cart'
	import Navigation from '$lib/components/Navigation.svelte'


	let isLoading = true
	let currentLocale = 'en'
	let rtl = false

	onMount(() => {
		// Async initialization function
		const initializeApp = async () => {
			// Initialize auth state from localStorage with token validation
			await authStore.initialize()

			// Initialize cart (will sync with backend if user is logged in)
			await cartActions.initialize()

			// Wait for i18n to be ready
			await waitLocale()

			// Set up locale reactivity
			currentLocale = getCurrentLocale()
			rtl = isRTL(currentLocale)

			isLoading = false
		}

		// Start async initialization
		initializeApp()

		// Subscribe to locale changes
		const unsubscribe = locale.subscribe(value => {
			if (value) {
				currentLocale = value
				rtl = isRTL(value)

				// Update document attributes
				if (browser) {
					document.documentElement.lang = value
					document.documentElement.dir = rtl ? 'rtl' : 'ltr'
				}
			}
		})

		// Return cleanup function
		return unsubscribe
	})
</script>

<svelte:head>
	<title>Hebrew Book Store - Learn Hebrew Level Bet</title>
	<meta name="description" content="Interactive online book for learning Hebrew at intermediate level. Features video lessons, exercises, and progress tracking." />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
</svelte:head>

{#if isLoading}
	<div class="min-h-screen flex items-center justify-center" style="background-color: var(--color-bg-primary);">
		<div class="text-center">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto mb-4"></div>
			<p class="text-primary-600">Loading...</p>
		</div>
	</div>
{:else}
	<div class="min-h-screen" style="background-color: var(--color-bg-primary);" class:rtl>
		<Navigation />
		<main class="flex-1">
			<slot />
		</main>

		<!-- Footer -->
		<footer class="border-t mt-auto" style="background-color: var(--color-bg-secondary); border-color: var(--color-border-light);">
			<div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
				<div class="text-center font-body text-sm" style="color: var(--color-text-secondary);">
					<p>&copy; 2024 Hebrew Book Store. All rights reserved.</p>
					<p class="mt-2">
						Learn Hebrew with our interactive online book platform
					</p>

				</div>
			</div>
		</footer>


	</div>
{/if}

<style>
	:global(.rtl) {
		direction: rtl;
	}

	:global(.rtl *) {
		text-align: inherit;
	}
</style>
