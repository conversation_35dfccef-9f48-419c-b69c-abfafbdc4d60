import { Migration } from '@mikro-orm/migrations';

export class Migration20250616115326 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "chapter" ("id" text not null, "title" text not null, "content" text not null, "preview_content" text not null, "difficulty_level" text not null, "order_in_book" integer not null, "is_published" boolean not null default false, "language" text not null default 'he', "price" integer null, "is_free" boolean not null default false, "audio_url" text null, "video_url" text null, "reading_time_minutes" integer null, "tags" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "chapter_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_chapter_deleted_at" ON "chapter" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "reading_progress" ("id" text not null, "user_id" text not null, "chapter_id" text not null, "progress_percentage" integer not null default 0, "last_position" text null, "time_spent_minutes" integer not null default 0, "completed_at" timestamptz null, "bookmarked" boolean not null default false, "notes" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "reading_progress_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_reading_progress_deleted_at" ON "reading_progress" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "subscription" ("id" text not null, "user_id" text not null, "type" text not null, "price" integer not null, "starts_at" timestamptz not null, "expires_at" timestamptz not null, "is_active" boolean not null default true, "auto_renew" boolean not null default false, "payment_method_id" text null, "stripe_subscription_id" text null, "cancelled_at" timestamptz null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "subscription_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_subscription_deleted_at" ON "subscription" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "translation" ("id" text not null, "entity_id" text not null, "entity_type" text not null, "language" text not null, "field_name" text not null, "translated_value" text not null, "is_approved" boolean not null default false, "translator_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "translation_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_translation_deleted_at" ON "translation" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "user_chapter_access" ("id" text not null, "user_id" text not null, "chapter_id" text not null, "access_type" text not null, "purchased_at" timestamptz null, "expires_at" timestamptz null, "payment_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "user_chapter_access_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_user_chapter_access_deleted_at" ON "user_chapter_access" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "chapter" cascade;`);

    this.addSql(`drop table if exists "reading_progress" cascade;`);

    this.addSql(`drop table if exists "subscription" cascade;`);

    this.addSql(`drop table if exists "translation" cascade;`);

    this.addSql(`drop table if exists "user_chapter_access" cascade;`);
  }

}
