{"name": "frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@types/js-cookie": "^3.0.6", "js-cookie": "^3.0.5", "svelte-i18n": "^4.0.1"}}