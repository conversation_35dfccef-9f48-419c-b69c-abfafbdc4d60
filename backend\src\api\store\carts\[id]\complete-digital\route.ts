import { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import createDigitalProductOrderWorkflow from "../../../../../workflows/create-digital-product-order"

export const POST = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  console.log('🔔 DIGITAL CART COMPLETION ENDPOINT CALLED')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('🛒 Cart ID:', req.params.id)
  console.log('👤 Request User:', 'Available in request context')
  console.log('🌐 Request Headers:', JSON.stringify({
    'content-type': req.headers['content-type'],
    'user-agent': req.headers['user-agent'],
    'authorization': req.headers['authorization'] ? 'present' : 'missing'
  }, null, 2))

  try {
    console.log('🚀 Starting digital product order workflow')

    const { result } = await createDigitalProductOrderWorkflow(req.scope)
      .run({
        input: {
          cart_id: req.params.id,
        },
      })

    console.log('✅ DIGITAL CART COMPLETION SUCCESSFUL')
    console.log('📋 Result type:', typeof result)
    console.log('📋 Result keys:', Object.keys(result || {}))
    console.log('🔄 Order ID:', result?.order_id || 'Unknown')
    console.log('🔄 Digital Product Order ID:', result?.digital_product_order?.id || 'Unknown')
    console.log('🔄 Success:', result?.success)
    console.log('=' .repeat(80))

    res.json({
      type: "order",
      ...result,
    })
  } catch (error: any) {
    console.error('❌ DIGITAL CART COMPLETION FAILED')
    console.error('  - Cart ID:', req.params.id)
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack:', error.stack)
    console.error('=' .repeat(80))

    res.status(500).json({
      type: "error",
      error: error.message,
      code: error.code || 'unknown_error'
    })
  }
}