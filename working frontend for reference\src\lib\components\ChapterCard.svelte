<script lang="ts">
  import { _ } from '$lib/i18n'
  import { getCurrentLocale, isRTL } from '$lib/i18n'
  import type { Chapter } from '$lib/api/client'
  import { goto } from '$app/navigation'
  import { formatPriceWithCurrency } from '$lib/stores/currency'

  export let chapter: Chapter

  $: currentLocale = getCurrentLocale()
  $: rtl = isRTL(currentLocale)
  $: title = getLocalizedTitle(chapter, currentLocale)
  $: description = getLocalizedDescription(chapter, currentLocale)

  function getLocalizedTitle(chapter: Chapter, locale: string): string {
    switch (locale) {
      case 'he':
        return chapter.title
      case 'en':
        return chapter.title_en || chapter.title
      case 'ru':
        return chapter.title_ru || chapter.title
      default:
        return chapter.title
    }
  }

  function getLocalizedDescription(chapter: Chapter, locale: string): string {
    switch (locale) {
      case 'he':
        return chapter.preview_content || chapter.description || ''
      case 'en':
        return chapter.preview_content_en || chapter.description_en || chapter.description || ''
      case 'ru':
        return chapter.preview_content_ru || chapter.description || ''
      default:
        return chapter.preview_content || chapter.description || ''
    }
  }



  function handleCardClick() {
    goto(`/chapters/${chapter.id}`)
  }

  function formatPrice(price?: number, currency?: string): string {
    if (!price) return $_('common.free')

    return formatPriceWithCurrency(price, currency)
  }
</script>

<div
  class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 cursor-pointer overflow-hidden"
  class:rtl
  on:click={handleCardClick}
  on:keydown={(e) => e.key === 'Enter' && handleCardClick()}
  role="button"
  tabindex="0"
>
  <!-- Header -->
  <div class="p-6 pb-4">
    <div class="flex items-start justify-between mb-3">
      <div class="flex items-center space-x-2" class:space-x-reverse={rtl}>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {$_('chapters.chapter')} {chapter.chapter_number || chapter.order_in_book}
        </span>
        

        
        {#if chapter.is_free}
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {$_('common.free')}
          </span>
        {/if}
      </div>
      
      {#if chapter.estimated_reading_time || chapter.reading_time_minutes}
        <span class="text-sm text-gray-500 flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {chapter.estimated_reading_time || chapter.reading_time_minutes} {$_('chapters.minutes')}
        </span>
      {/if}
    </div>

    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2" class:text-right={rtl}>
      {title}
    </h3>

    {#if description}
      <p class="text-gray-600 text-sm line-clamp-3 mb-4" class:text-right={rtl}>
        {description}
      </p>
    {/if}
  </div>

  <!-- Footer -->
  <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4" class:space-x-reverse={rtl}>
        <!-- Media indicators -->
        <div class="flex items-center space-x-2" class:space-x-reverse={rtl}>
          {#if chapter.video_url}
            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
          {/if}
          {#if chapter.audio_url}
            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.816L4.846 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.846l3.537-3.816a1 1 0 011.617.816z" clip-rule="evenodd" />
            </svg>
          {/if}
          {#if chapter.exercise_url}
            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8z" clip-rule="evenodd" />
            </svg>
          {/if}
        </div>
      </div>

      <!-- Action button -->
      <div>
        {#if chapter.is_free}
          <button
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            on:click|stopPropagation={handleCardClick}
          >
            {$_('chapters.read_chapter')}
          </button>
        {:else}
          <div class="flex items-center space-x-2" class:space-x-reverse={rtl}>
            <span class="text-sm font-medium text-gray-900">
              {formatPrice(chapter.price, 'USD')}
            </span>
            <button
              class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              on:click|stopPropagation={() => goto(`/purchase/${chapter.id}`)}
            >
              {$_('common.buy')}
            </button>
          </div>
        {/if}
      </div>
    </div>
  </div>
</div>

<style>
  .rtl {
    direction: rtl;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
