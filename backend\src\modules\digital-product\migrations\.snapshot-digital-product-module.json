{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "digital_product", "schema": "public", "indexes": [{"keyName": "IDX_digital_product_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_digital_product_deleted_at\" ON \"digital_product\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "digital_product_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["main", "preview"], "mappedType": "enum"}, "fileId": {"name": "fileId", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "mimeType": {"name": "mimeType", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "url": {"name": "url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "digital_product_id": {"name": "digital_product_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "digital_product_media", "schema": "public", "indexes": [{"keyName": "IDX_digital_product_media_digital_product_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_digital_product_media_digital_product_id\" ON \"digital_product_media\" (digital_product_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_digital_product_media_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_digital_product_media_deleted_at\" ON \"digital_product_media\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "digital_product_media_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"digital_product_media_digital_product_id_foreign": {"constraintName": "digital_product_media_digital_product_id_foreign", "columnNames": ["digital_product_id"], "localTableName": "public.digital_product_media", "referencedColumnNames": ["id"], "referencedTableName": "public.digital_product", "deleteRule": "cascade", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["pending", "sent"], "mappedType": "enum"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "digital_product_order", "schema": "public", "indexes": [{"keyName": "IDX_digital_product_order_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_digital_product_order_deleted_at\" ON \"digital_product_order\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "digital_product_order_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"digital_product_order_id": {"name": "digital_product_order_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "digital_product_id": {"name": "digital_product_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}}, "name": "digitalproduct_digitalproductorders", "schema": "public", "indexes": [{"keyName": "digitalproduct_digitalproductorders_pkey", "columnNames": ["digital_product_order_id", "digital_product_id"], "composite": true, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"digitalproduct_digitalproductorders_digital_prod_c0c21_foreign": {"constraintName": "digitalproduct_digitalproductorders_digital_prod_c0c21_foreign", "columnNames": ["digital_product_order_id"], "localTableName": "public.digitalproduct_digitalproductorders", "referencedColumnNames": ["id"], "referencedTableName": "public.digital_product_order", "deleteRule": "cascade", "updateRule": "cascade"}, "digitalproduct_digitalproductorders_digital_product_id_foreign": {"constraintName": "digitalproduct_digitalproductorders_digital_product_id_foreign", "columnNames": ["digital_product_id"], "localTableName": "public.digitalproduct_digitalproductorders", "referencedColumnNames": ["id"], "referencedTableName": "public.digital_product", "deleteRule": "cascade", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}