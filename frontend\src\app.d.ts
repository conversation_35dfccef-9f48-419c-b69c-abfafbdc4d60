// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

// Vite environment variables
interface ImportMetaEnv {
	readonly VITE_BACKEND_URL: string
	readonly VITE_MEDUSA_PUBLISHABLE_API_KEY: string
	readonly VITE_APP_NAME: string
	readonly VITE_APP_URL: string
}

interface ImportMeta {
	readonly env: ImportMetaEnv
}

export {};
