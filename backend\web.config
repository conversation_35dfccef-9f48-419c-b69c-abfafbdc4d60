<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- Specify Node.js version -->
    <iisnode nodeProcessCommandLine="C:\Program Files\nodejs\node.exe" />

    <!-- Application settings -->
    <appSettings>
      <add key="WEBSITE_NODE_DEFAULT_VERSION" value="20-lts" />
      <add key="NODE_ENV" value="production" />
    </appSettings>

    <handlers>
      <!-- Custom startup command for MedusaJS -->
      <add name="iisnode" path="startup.sh" verb="*" modules="iisnode"/>
    </handlers>

    <rewrite>
      <rules>
        <rule name="MedusaJS">
          <match url="/*" />
          <action type="Rewrite" url="startup.sh"/>
        </rule>
      </rules>
    </rewrite>

    <httpErrors existingResponse="PassThrough" />
  </system.webServer>
</configuration>
