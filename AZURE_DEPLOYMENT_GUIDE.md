# 🚀 Complete MedusaJS Deployment Guide for Azure

This guide covers the complete deployment process for MedusaJS backend and admin panel using GitHub Actions and Azure App Service.

## 📋 **Overview**

Our deployment stack:
- **Source Control**: GitHub with Actions for CI/CD
- **Backend Hosting**: Azure App Service (Linux, Node.js 20)
- **Database**: Azure Database for PostgreSQL
- **Admin Panel**: Built-in MedusaJS admin (served from backend)

## 🏗️ **Architecture**

```
GitHub Repository
    ↓ (Push to main)
GitHub Actions
    ↓ (Build & Deploy)
Azure App Service
    ↓ (Connects to)
Azure PostgreSQL Database
```

---

## 1️⃣ **GitHub Repository Setup**

### **Required Files Structure:**
```
your-repo/
├── backend/
│   ├── src/
│   ├── package.json
│   ├── medusa-config.ts
│   └── startup.sh
├── .github/
│   └── workflows/
│       └── main_your-app.yml
└── README.md
```

### **Key Files:**

#### **backend/startup.sh** (Make executable)
```bash
#!/bin/bash

# Official MedusaJS startup script based on documentation
export NODE_ENV=production
export YARN_SILENT=1

echo "🚀 MedusaJS Official Startup Process - $(date)"
echo "📂 Working directory: $(pwd)"

# Official MedusaJS deployment process
if [ -d ".medusa/server" ]; then
    echo "✅ Found .medusa/server - following official deployment guide"
    cd .medusa/server

    # Remove any tsconfig.json that shouldn't be here
    if [ -f "tsconfig.json" ]; then
        echo "⚠️  Removing tsconfig.json from runtime directory"
        rm tsconfig.json
    fi

    echo "📦 Installing production dependencies..."
    if [ -f "package.json" ]; then
        yarn install --production --frozen-lockfile --silent
    else
        echo "❌ No package.json found in .medusa/server"
        cd ..
        yarn install --production --frozen-lockfile --silent
        cd server
    fi

    echo "🔄 Running predeploy (migrations)..."
    yarn predeploy || echo "⚠️  Predeploy failed, continuing..."

    echo "🎯 Starting MedusaJS server..."
    exec yarn start
else
    echo "❌ No .medusa/server directory found!"
    echo "📋 Attempting fallback startup from root..."

    if [ -f "package.json" ]; then
        yarn install --frozen-lockfile --silent
        yarn predeploy || echo "⚠️  Predeploy failed, continuing..."
        exec yarn start
    else
        echo "❌ No package.json found - cannot start application"
        exit 1
    fi
fi
```

#### **backend/package.json** (Key scripts)
```json
{
  "scripts": {
    "build": "medusa build",
    "start": "medusa start",
    "dev": "medusa develop",
    "predeploy": "medusa migrations run"
  }
}
```

---

## 2️⃣ **Azure Database for PostgreSQL Setup**

### **Create Database:**
```bash
# Create resource group (if not exists)
az group create --name your-resource-group --location "East US"

# Create PostgreSQL server
az postgres server create \
  --resource-group your-resource-group \
  --name your-postgres-server \
  --location "East US" \
  --admin-user medusa_admin \
  --admin-password "YourSecurePassword123!" \
  --sku-name GP_Gen5_2 \
  --version 13

# Create database
az postgres db create \
  --resource-group your-resource-group \
  --server-name your-postgres-server \
  --name medusa_store

# Configure firewall (allow Azure services)
az postgres server firewall-rule create \
  --resource-group your-resource-group \
  --server your-postgres-server \
  --name AllowAzureServices \
  --start-ip-address 0.0.0.0 \
  --end-ip-address 0.0.0.0
```

### **Get Connection String:**
```bash
# Your DATABASE_URL will be:
******************************************************************************************************/medusa_store?sslmode=require
```

### **Manual Migration (One-time):**
```bash
# In your local development environment or Azure Cloud Shell:
cd backend
export DATABASE_URL="your-connection-string"
npx medusa migrations run
```

---

## 3️⃣ **Azure App Service Setup**

### **Create App Service:**
```bash
# Create App Service Plan
az appservice plan create \
  --name your-app-plan \
  --resource-group your-resource-group \
  --sku B1 \
  --is-linux

# Create Web App
az webapp create \
  --name your-medusa-app \
  --resource-group your-resource-group \
  --plan your-app-plan \
  --runtime "NODE|20-lts"
```

### **Configure App Service:**
```bash
# Set Node.js version
az webapp config set \
  --name your-medusa-app \
  --resource-group your-resource-group \
  --linux-fx-version "NODE|20-lts"

# Enable logging
az webapp log config \
  --name your-medusa-app \
  --resource-group your-resource-group \
  --application-logging filesystem \
  --level information
```

---

## 4️⃣ **Environment Variables Configuration**

### **Required Environment Variables:**

#### **Database & Redis:**
```bash
DATABASE_URL=postgresql://medusa_admin:<EMAIL>:5432/medusa_store?sslmode=require
REDIS_URL=redis://your-redis-instance:6379  # Optional for basic setup
```

#### **Security (Generate these!):**
```bash
# Generate secure secrets:
JWT_SECRET=$(openssl rand -base64 32)
COOKIE_SECRET=$(openssl rand -base64 32)

# Set in Azure:
JWT_SECRET=your-generated-jwt-secret
COOKIE_SECRET=your-generated-cookie-secret
```

#### **CORS & URLs:**
```bash
MEDUSA_BACKEND_URL=https://your-medusa-app.azurewebsites.net
ADMIN_CORS=https://your-medusa-app.azurewebsites.net
STORE_CORS=https://your-frontend-domain.com  # Your storefront URL
AUTH_CORS=https://your-medusa-app.azurewebsites.net,https://your-frontend-domain.com
```

#### **Admin Configuration:**
```bash
MEDUSA_ADMIN_ONBOARDING_TYPE=default
DISABLE_MEDUSA_ADMIN=false
```

#### **Payment Providers (Stripe Example):**
```bash
# Get from Stripe Dashboard (https://dashboard.stripe.com/apikeys)
STRIPE_API_KEY=sk_live_your_stripe_secret_key  # or sk_test_ for testing
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Publishable key (for frontend)
STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key  # or pk_test_ for testing
```

#### **File Storage (Optional - AWS S3):**
```bash
# Get from AWS IAM Console
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_BUCKET=your-bucket-name
```

#### **Email Service (Optional - SendGrid):**
```bash
# Get from SendGrid Dashboard
SENDGRID_API_KEY=SG.your-sendgrid-api-key
SENDGRID_FROM=<EMAIL>
```

### **Set Environment Variables in Azure:**
```bash
az webapp config appsettings set \
  --name your-medusa-app \
  --resource-group your-resource-group \
  --settings \
    NODE_ENV=production \
    DATABASE_URL="your-database-url" \
    JWT_SECRET="your-jwt-secret" \
    COOKIE_SECRET="your-cookie-secret" \
    MEDUSA_BACKEND_URL="https://your-medusa-app.azurewebsites.net" \
    ADMIN_CORS="https://your-medusa-app.azurewebsites.net" \
    MEDUSA_ADMIN_ONBOARDING_TYPE=default \
    DISABLE_MEDUSA_ADMIN=false \
    STRIPE_API_KEY="your-stripe-key" \
    STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"
```

---

## 5️⃣ **GitHub Actions Setup**

### **Get Azure Credentials:**
```bash
# Create service principal
az ad sp create-for-rbac \
  --name "github-actions-medusa" \
  --role contributor \
  --scopes /subscriptions/your-subscription-id/resourceGroups/your-resource-group \
  --sdk-auth

# Get publish profile
az webapp deployment list-publishing-profiles \
  --name your-medusa-app \
  --resource-group your-resource-group \
  --xml
```

### **GitHub Secrets:**
Add these secrets in GitHub Repository → Settings → Secrets:

```
AZUREAPPSERVICE_CLIENTID_xxx=your-client-id
AZUREAPPSERVICE_TENANTID_xxx=your-tenant-id
AZUREAPPSERVICE_SUBSCRIPTIONID_xxx=your-subscription-id
AZURE_WEBAPP_PUBLISH_PROFILE=<your-publish-profile-xml>
```

### **GitHub Actions Workflow:**
Create `.github/workflows/main_your-app.yml`:

```yaml
name: Deploy to Azure App Service

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'
          cache-dependency-path: backend/yarn.lock

      - name: Install dependencies
        run: |
          cd backend
          yarn install --frozen-lockfile

      - name: Build application
        run: |
          cd backend
          yarn build

      - name: Prepare deployment package
        run: |
          mkdir -p deploy
          cd backend

          # Copy built files
          if [ -d ".medusa" ]; then
            cp -r .medusa ../deploy/
          fi

          # Copy essential files
          cp package.json ../deploy/
          cp yarn.lock ../deploy/
          cp medusa-config.ts ../deploy/
          cp startup.sh ../deploy/
          chmod +x ../deploy/startup.sh

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: ./deploy

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'Production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}

    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_xxx }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_xxx }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_xxx }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'your-medusa-app'
          slot-name: 'Production'
          package: '.'
          startup-command: 'chmod +x startup.sh && ./startup.sh'
```

---

## 6️⃣ **Deployment Process**

### **Automatic Deployment:**
1. **Push to main branch** → Triggers GitHub Actions
2. **GitHub Actions builds** the MedusaJS app
3. **Creates deployment package** with built files
4. **Deploys to Azure App Service**
5. **Azure runs startup.sh** which:
   - Installs production dependencies
   - Runs database migrations
   - Starts the MedusaJS server

### **Manual Steps (One-time):**
1. **Create admin user:**
   ```bash
   # SSH into Azure App Service or use Cloud Shell
   az webapp ssh --name your-medusa-app --resource-group your-resource-group

   # Inside the container:
   cd .medusa/server
   npx medusa user -e <EMAIL> -p your-secure-password
   ```

---

## 7️⃣ **Access Your Application**

### **URLs:**
- **Backend API**: `https://your-medusa-app.azurewebsites.net`
- **Admin Panel**: `https://your-medusa-app.azurewebsites.net/app`
- **Health Check**: `https://your-medusa-app.azurewebsites.net/health`

### **Admin Login:**
Use the credentials you created with the `npx medusa user` command.

---

## 8️⃣ **Troubleshooting**

### **View Logs:**
```bash
# Stream logs
az webapp log tail --name your-medusa-app --resource-group your-resource-group

# Download logs
az webapp log download --name your-medusa-app --resource-group your-resource-group
```

### **Common Issues:**

#### **Database Connection:**
- Ensure firewall allows Azure services
- Check connection string format
- Verify SSL mode is required

#### **Admin Panel 404:**
- Check `DISABLE_MEDUSA_ADMIN=false`
- Verify `ADMIN_CORS` includes your app URL
- Ensure build process completed successfully

#### **Startup Timeout:**
- Check if dependencies are pre-installed
- Verify startup.sh is executable
- Monitor logs for specific errors

---

## 9️⃣ **Security Checklist**

- [ ] Use strong, randomly generated secrets
- [ ] Enable SSL/HTTPS (automatic with Azure App Service)
- [ ] Configure proper CORS origins
- [ ] Use production Stripe keys (not test keys)
- [ ] Enable database SSL mode
- [ ] Regularly update dependencies
- [ ] Monitor application logs

---

## 🎯 **Quick Start Commands**

```bash
# 1. Create all Azure resources
./scripts/create-azure-resources.sh

# 2. Set environment variables
./scripts/set-environment-variables.sh

# 3. Deploy via GitHub Actions
git push origin main

# 4. Create admin user
az webapp ssh --name your-app --resource-group your-rg
npx medusa user -e <EMAIL> -p password
```

---

## 📚 **Additional Resources**

- [MedusaJS Official Deployment Guide](https://docs.medusajs.com/learn/deployment/general)
- [Azure App Service Documentation](https://docs.microsoft.com/en-us/azure/app-service/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)

---

**✅ This guide reflects our working deployment setup that successfully deploys MedusaJS backend and admin panel to Azure App Service using GitHub Actions.**

---

## 🔧 **How to Get Required Keys and Secrets**

### **Stripe Keys:**
1. **Sign up at**: https://dashboard.stripe.com
2. **Get API Keys**: Dashboard → Developers → API keys
   - **Publishable key**: `pk_test_...` (for frontend)
   - **Secret key**: `sk_test_...` (for backend)
3. **Webhook Secret**: Dashboard → Developers → Webhooks → Add endpoint
   - **Endpoint URL**: `https://your-app.azurewebsites.net/webhooks/stripe`
   - **Events**: Select relevant events (payment_intent.succeeded, etc.)
   - **Copy webhook secret**: `whsec_...`

### **SendGrid Keys (Email):**
1. **Sign up at**: https://sendgrid.com
2. **Create API Key**: Settings → API Keys → Create API Key
3. **Verify sender**: Settings → Sender Authentication

### **AWS S3 Keys (File Storage):**
1. **AWS Console**: https://console.aws.amazon.com
2. **Create IAM User**: IAM → Users → Add user
3. **Attach Policy**: AmazonS3FullAccess
4. **Create Access Key**: Security credentials → Create access key

### **Generate Secure Secrets:**
```bash
# Generate JWT_SECRET (32 bytes, base64 encoded)
openssl rand -base64 32

# Generate COOKIE_SECRET (32 bytes, base64 encoded)
openssl rand -base64 32
```

---

## 🎯 **Testing Your Deployment**

### **1. Test Backend API:**
```bash
curl https://your-medusa-app.azurewebsites.net/health
```

### **2. Test Admin Panel:**
Visit: `https://your-medusa-app.azurewebsites.net/app`

### **3. Test Store API:**
```bash
curl https://your-medusa-app.azurewebsites.net/store/products
```

---

## � **Tips for Success**

### **Development Workflow:**
1. **Develop locally** with `yarn dev`
2. **Test build** with `yarn build` before pushing
3. **Push to main** to trigger deployment
4. **Monitor logs** during deployment
5. **Test admin panel** after deployment

### **Database Management:**
- **Run migrations manually** first time: `npx medusa migrations run`
- **Use Azure Cloud Shell** for database operations
- **Backup database** regularly
- **Monitor connection limits**

### **Performance Optimization:**
- **Use CDN** for static assets
- **Enable compression** in Azure App Service
- **Monitor memory usage** and scale accordingly
- **Use Redis** for session storage (optional)

---

**🎉 Your MedusaJS backend and admin panel are now successfully deployed on Azure App Service!**

This guide reflects our tested and working deployment process that successfully runs MedusaJS v2.8.4 with the built-in admin panel on Azure infrastructure.
