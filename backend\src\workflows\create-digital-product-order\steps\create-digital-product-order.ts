import {
  createStep,
  StepResponse,
} from "@medusajs/framework/workflows-sdk"
import {
  OrderLineItemDTO,
  ProductVariantDTO,
  InferTypeOf,
} from "@medusajs/framework/types"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import { OrderStatus } from "../../../modules/digital-product/types"
import DigitalProductModuleService from "../../../modules/digital-product/service"
import { DIGITAL_PRODUCT_MODULE } from "../../../modules/digital-product"
import DigitalProduct from "../../../modules/digital-product/models/digital-product"

type StepInput = {
  items?: any[] // Optional items array
  order_id?: string // Optional order ID for lookup
}

const createDigitalProductOrderStep = createStep(
  "create-digital-product-order",
  async ({ items = [], order_id }: StepInput, { container }) => {
    const digitalProductModuleService: DigitalProductModuleService =
      container.resolve(DIGITAL_PRODUCT_MODULE)

    let digitalProductIds: string[] = []

    if (items.length > 0) {
      // Use provided items
      digitalProductIds = items
        .filter((item) => item?.variant?.digital_product)
        .map((item) => item.variant.digital_product.id)
    } else if (order_id) {
      // Look up order to find digital products
      const query = container.resolve(ContainerRegistrationKeys.QUERY)

      const { data: orders } = await query.graph({
        entity: "order",
        fields: [
          "items.variant.digital_product.id",
        ],
        filters: {
          id: order_id,
        },
      })

      if (orders[0]?.items) {
        orders[0].items.forEach((item: any) => {
          if (item?.variant?.digital_product?.id) {
            digitalProductIds.push(item.variant.digital_product.id)
          }
        })
      }
    }

    // Only create digital product order if we have digital products
    if (digitalProductIds.length === 0) {
      throw new Error("No digital products found in order items")
    }

    const digitalProductOrder = await digitalProductModuleService
      .createDigitalProductOrders({
        status: OrderStatus.PENDING,
        products: digitalProductIds,
      })

    return new StepResponse({
      digital_product_order: digitalProductOrder,
    }, {
      digital_product_order: digitalProductOrder,
    })
  },
  async ({ digital_product_order }: { digital_product_order: any }, { container }) => {
    const digitalProductModuleService: DigitalProductModuleService = 
      container.resolve(DIGITAL_PRODUCT_MODULE)

    await digitalProductModuleService.deleteDigitalProductOrders(
      digital_product_order?.id
    )
  }
)

export default createDigitalProductOrderStep