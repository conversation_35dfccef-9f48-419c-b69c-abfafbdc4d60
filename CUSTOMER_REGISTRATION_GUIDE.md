# Customer Registration System - Implementation Guide

## 🎯 Overview

This document outlines the complete customer registration functionality implemented for the Hebrew Book Store, following MedusaJS v2 best practices and official documentation.

## 📋 Implementation Summary

### ✅ **What's Implemented:**

#### **1. Backend API Integration**
- **Registration API**: `/store/auth/customer/emailpass/register`
- **Login API**: `/store/auth/customer/emailpass`
- **Customer Creation API**: `/store/customers`
- **Customer Profile API**: `/store/customers/me`
- **Logout API**: `/store/auth/session`

#### **2. Frontend Components**
- **AuthModal**: Main authentication modal with login/register modes
- **RegistrationForm**: Complete registration form with validation
- **LoginForm**: Login form with validation
- **Navigation**: Updated with authentication buttons and user menu
- **Account Page**: Customer account management page

#### **3. State Management**
- **Auth Store**: Centralized authentication state management
- **Local Storage**: Persistent authentication across sessions
- **Reactive UI**: Real-time authentication state updates

#### **4. User Experience**
- **Responsive Design**: Works on desktop and mobile
- **Form Validation**: Client-side validation with error messages
- **Loading States**: Visual feedback during API calls
- **Error Handling**: Comprehensive error messages
- **Internationalization**: Multi-language support

## 🔧 Technical Implementation

### **Authentication Flow (Following MedusaJS v2 Documentation)**

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant MedusaAPI
    
    User->>Frontend: Fill registration form
    Frontend->>MedusaAPI: POST /auth/customer/emailpass/register
    alt Registration Success
        MedusaAPI->>Frontend: JWT Token
        Frontend->>MedusaAPI: POST /customers (with JWT)
        MedusaAPI->>Frontend: Customer Profile
        Frontend->>User: Registration Success
    else User Already Exists
        MedusaAPI->>Frontend: Error (existing user)
        Frontend->>MedusaAPI: POST /auth/customer/emailpass (login)
        MedusaAPI->>Frontend: JWT Token
        Frontend->>MedusaAPI: POST /customers (with JWT)
        MedusaAPI->>Frontend: Customer Profile
        Frontend->>User: Registration Success (existing user)
    end
```

### **Key Files Created/Modified:**

#### **New Files:**
- `frontend/src/lib/stores/auth.ts` - Authentication store
- `frontend/src/lib/components/AuthModal.svelte` - Main auth modal
- `frontend/src/lib/components/RegistrationForm.svelte` - Registration form
- `frontend/src/lib/components/LoginForm.svelte` - Login form
- `frontend/src/routes/account/+page.svelte` - Account page

#### **Modified Files:**
- `frontend/src/lib/api/client.ts` - Added auth API methods
- `frontend/src/lib/components/Navigation.svelte` - Added auth UI
- `frontend/src/routes/+layout.svelte` - Auth initialization
- `frontend/src/lib/i18n/locales/en.json` - Auth translations

## 🚀 How to Use

### **For Users:**

#### **Registration:**
1. Click "Create Account" in navigation
2. Fill out registration form:
   - Email (required)
   - Password (required, min 8 characters)
   - Confirm Password (required)
   - First Name (required)
   - Last Name (optional)
   - Phone (optional)
3. Submit form
4. Automatically logged in upon success

#### **Login:**
1. Click "Sign In" in navigation
2. Enter email and password
3. Submit form
4. Redirected to account page

#### **Account Management:**
1. Access account page via navigation when logged in
2. View personal information
3. See account details (ID, member since, etc.)
4. Logout when needed

### **For Developers:**

#### **Using the Auth Store:**
```typescript
import { authStore, isAuthenticated, currentCustomer } from '$lib/stores/auth'

// Register a new customer
const result = await authStore.register({
  email: '<EMAIL>',
  password: 'password123',
  first_name: 'John',
  last_name: 'Doe'
})

// Login existing customer
const result = await authStore.login({
  email: '<EMAIL>',
  password: 'password123'
})

// Logout
await authStore.logout()

// Check authentication state
$: if ($isAuthenticated) {
  console.log('User is logged in:', $currentCustomer)
}
```

## 🔒 Security Features

### **Frontend Security:**
- **Input Validation**: Email format, password strength
- **XSS Protection**: Proper input sanitization
- **CSRF Protection**: JWT tokens for API calls
- **Secure Storage**: JWT tokens in localStorage (consider httpOnly cookies for production)

### **Backend Security (MedusaJS):**
- **Password Hashing**: Handled by MedusaJS auth module
- **JWT Tokens**: Secure authentication tokens
- **Rate Limiting**: Built into MedusaJS
- **CORS Configuration**: Proper CORS setup required

## ⚙️ Admin Configuration Required

### **🔴 IMPORTANT: Admin Actions Needed**

#### **1. CORS Configuration**
The backend needs proper CORS configuration to allow frontend requests:

```javascript
// In medusa-config.js
module.exports = {
  projectConfig: {
    cors: {
      origin: [
        "http://localhost:5173", // Frontend dev server
        "https://yourdomain.com" // Production domain
      ],
      credentials: true
    }
  }
}
```

#### **2. Publishable API Key**
Create a publishable API key in MedusaJS admin:
1. Go to MedusaJS Admin (`http://localhost:9000/app`)
2. Navigate to Settings → API Key Management
3. Create new Publishable API Key
4. Add to frontend environment variables:
```bash
# frontend/.env.local
PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_your_key_here
```

#### **3. Region Configuration**
Ensure at least one region is configured:
1. Go to MedusaJS Admin
2. Navigate to Settings → Regions
3. Create/verify region with currency (USD, EUR, ILS, etc.)

#### **4. Email Provider (Optional)**
For password reset functionality (future):
1. Configure email provider in MedusaJS
2. Set up email templates

## 🧪 Testing the Registration Process

### **Manual Testing Steps:**

#### **1. Test Registration:**
```bash
# 1. Start both servers
cd backend && npm run dev
cd frontend && npm run dev

# 2. Open browser to http://localhost:5173
# 3. Click "Create Account"
# 4. Fill form with test data:
#    - Email: <EMAIL>
#    - Password: password123
#    - First Name: Test
#    - Last Name: User
# 5. Submit and verify success
```

#### **2. Test Login:**
```bash
# 1. Logout if logged in
# 2. Click "Sign In"
# 3. Enter same credentials
# 4. Verify login success
```

#### **3. Test Account Page:**
```bash
# 1. While logged in, click user name in navigation
# 2. Verify account information displays
# 3. Test logout functionality
```

### **API Testing with cURL:**

#### **Registration:**
```bash
curl -X POST http://localhost:9000/store/auth/customer/emailpass/register \
  -H "Content-Type: application/json" \
  -H "x-publishable-api-key: pk_your_key_here" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### **Customer Creation:**
```bash
curl -X POST http://localhost:9000/store/customers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "x-publishable-api-key: pk_your_key_here" \
  -d '{
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User"
  }'
```

## 🐛 Common Issues & Solutions

### **1. CORS Errors**
**Problem**: "Access to fetch blocked by CORS policy"
**Solution**: Configure CORS in `medusa-config.js` as shown above

### **2. 401 Unauthorized**
**Problem**: "401 Unauthorized" on customer creation
**Solution**: Ensure JWT token is properly passed in Authorization header

### **3. Missing Publishable API Key**
**Problem**: "Publishable API key required"
**Solution**: Create and configure publishable API key in admin

### **4. Region Not Found**
**Problem**: "No regions found"
**Solution**: Create at least one region in MedusaJS admin

## 🔮 Future Enhancements

### **Planned Features:**
- **Password Reset**: Email-based password recovery
- **Email Verification**: Verify email addresses on registration
- **Social Login**: Google, Facebook, GitHub integration
- **Two-Factor Authentication**: Enhanced security
- **Customer Orders**: Order history and management
- **Profile Editing**: Update customer information

### **Technical Improvements:**
- **HttpOnly Cookies**: More secure token storage
- **Refresh Tokens**: Automatic token renewal
- **Rate Limiting**: Frontend rate limiting
- **Error Tracking**: Comprehensive error monitoring

## 📞 Support

For issues with the customer registration system:
1. Check browser console for errors
2. Verify CORS configuration
3. Ensure publishable API key is set
4. Check MedusaJS backend logs
5. Verify region configuration in admin

The implementation follows MedusaJS v2 official documentation and best practices for a secure, scalable customer authentication system.
