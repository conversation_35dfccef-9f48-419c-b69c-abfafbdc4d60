import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"
import NOTIFICATION_MODULE from "../../../modules/book"
import Stripe from "stripe"

const stripe = new Stripe(process.env.STRIPE_API_KEY!, {
  apiVersion: "2025-05-28.basil"
})

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log('🔔 STRIPE WEBHOOK RECEIVED')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('🌐 Request Headers:', JSON.stringify({
    'content-type': req.headers['content-type'],
    'stripe-signature': req.headers['stripe-signature'] ? 'present' : 'missing',
    'user-agent': req.headers['user-agent']
  }, null, 2))

  const sig = req.headers['stripe-signature'] as string
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

  console.log('📋 Webhook Details:')
  console.log('  - Body type:', typeof req.body)
  console.log('  - Signature present:', !!sig)
  console.log('  - Webhook secret configured:', !!webhookSecret)

  let event: Stripe.Event

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body as string, sig, webhookSecret)
    console.log('✅ Webhook signature verified successfully')
  } catch (err: any) {
    console.error('❌ WEBHOOK SIGNATURE VERIFICATION FAILED')
    console.error('  - Error:', err.message)
    console.error('  - Signature preview:', sig?.substring(0, 50) + '...')
    return res.status(400).json({ error: 'Invalid signature' })
  }

  console.log('🎯 STRIPE EVENT DETAILS:')
  console.log('  - Event Type:', event.type)
  console.log('  - Event ID:', event.id)
  console.log('  - Created:', new Date(event.created * 1000).toISOString())
  console.log('  - Live Mode:', event.livemode)
  console.log('  - API Version:', event.api_version)

  const bookService = req.scope.resolve(BOOK_MODULE)
  const notificationService = req.scope.resolve(NOTIFICATION_MODULE as any)

  try {
    console.log('🔄 Processing webhook event:', event.type)

    switch (event.type) {
      case 'checkout.session.completed':
        console.log('💳 Processing checkout session completion')
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session, bookService, notificationService)
        break

      case 'payment_intent.succeeded':
        console.log('✅ Processing payment intent success')
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent, bookService)
        break

      case 'payment_intent.payment_failed':
        console.log('❌ Processing payment intent failure')
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent, notificationService)
        break

      case 'invoice.payment_succeeded':
        console.log('📄 Processing invoice payment success')
        await handleSubscriptionPayment(event.data.object as Stripe.Invoice, bookService, notificationService)
        break

      case 'customer.subscription.deleted':
        console.log('🗑️ Processing subscription deletion')
        await handleSubscriptionCancelled(event.data.object as Stripe.Subscription, bookService, notificationService)
        break

      case 'customer.subscription.updated':
        console.log('🔄 Processing subscription update')
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription, bookService)
        break

      default:
        console.log(`ℹ️ Unhandled event type: ${event.type}`)
        console.log('  - Event data preview:', JSON.stringify(event.data.object, null, 2).substring(0, 500))
    }

    console.log('✅ WEBHOOK PROCESSING COMPLETED')
    console.log('  - Event Type:', event.type)
    console.log('  - Event ID:', event.id)
    console.log('=' .repeat(80))

    res.json({ received: true })
  } catch (error: any) {
    console.error('❌ CRITICAL ERROR PROCESSING WEBHOOK')
    console.error('  - Event Type:', event.type)
    console.error('  - Event ID:', event.id)
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack:', error.stack)
    console.error('=' .repeat(80))
    res.status(500).json({ error: 'Webhook processing failed' })
  }
}

async function handleCheckoutCompleted(
  session: Stripe.Checkout.Session,
  bookService: any,
  notificationService: any
) {
  console.log('Processing checkout completion:', session.id)

  const { user_id, subscription_type, items } = session.metadata || {}

  if (subscription_type) {
    // Handle subscription purchase
    const subscriptionData = {
      user_id,
      type: subscription_type,
      price: session.amount_total!, // Convert from cents
      starts_at: new Date(),
      expires_at: new Date(Date.now() + (subscription_type === '6_months' ? 6 : 12) * 30 * 24 * 60 * 60 * 1000),
      is_active: true,
      stripe_subscription_id: session.subscription as string,
      payment_method_id: session.payment_method_options?.card?.setup_future_usage ? 'saved' : null
    }

    try {
      const subscription = await bookService.createSubscription(subscriptionData)
      console.log('Subscription created:', subscription.id)

      // Send confirmation email
      if (user_id) {
        const user = { id: user_id, email: session.customer_email, first_name: 'User' } // Mock user
        await notificationService.sendSubscriptionConfirmation(user, subscription)
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
    }
  } else if (items) {
    // Handle individual chapter purchases
    const purchasedItems = JSON.parse(items)
    
    for (const item of purchasedItems) {
      try {
        await bookService.grantChapterAccess(
          user_id,
          item.chapter_id,
          'purchase',
          session.payment_intent as string
        )
        console.log(`Access granted for chapter ${item.chapter_id} to user ${user_id}`)
      } catch (error) {
        console.error(`Error granting access for chapter ${item.chapter_id}:`, error)
      }
    }

    // Send purchase confirmation email
    if (user_id) {
      const user = { id: user_id, email: session.customer_email, first_name: 'User' }
      await notificationService.sendPurchaseConfirmation(user, purchasedItems)
    }
  }
}

async function handlePaymentSucceeded(
  paymentIntent: Stripe.PaymentIntent,
  bookService: any
) {
  console.log('Payment succeeded:', paymentIntent.id)
  
  // Update payment status in database
  // This would typically update a payments table
  console.log(`Payment ${paymentIntent.id} for amount ${paymentIntent.amount } succeeded`)
}

async function handlePaymentFailed(
  paymentIntent: Stripe.PaymentIntent,
  notificationService: any
) {
  console.log('Payment failed:', paymentIntent.id)
  
  // Notify user of payment failure
  const customerEmail = paymentIntent.receipt_email
  if (customerEmail) {
    // In a real app, you'd send a payment failure notification
    console.log(`Would send payment failure notification to ${customerEmail}`)
  }
}

async function handleSubscriptionPayment(
  invoice: Stripe.Invoice,
  bookService: any,
  notificationService: any
) {
  console.log('Subscription payment succeeded:', invoice.id)
  
/*  const subscriptionId = (invoice as any).subscription as string | null;
  if (subscriptionId) {
    // Update subscription status
    try {
      const subscription = await bookService.listSubscriptions({
        stripe_subscription_id: subscriptionId
      })
      
      if (subscription.length > 0) {
        await bookService.updateSubscription(subscription[0].id, {
          is_active: true,
          updated_at: new Date()
        })
        console.log(`Subscription ${subscription[0].id} renewed`)
      }
    } catch (error) {
      console.error('Error updating subscription:', error)
    }
  }*/
}

async function handleSubscriptionCancelled(
  subscription: Stripe.Subscription,
  bookService: any,
  notificationService: any
) {
  console.log('Subscription cancelled:', subscription.id)
  
  try {
    const localSubscriptions = await bookService.listSubscriptions({
      stripe_subscription_id: subscription.id
    })
    
    if (localSubscriptions.length > 0) {
      await bookService.cancelSubscription(localSubscriptions[0].id)
      console.log(`Local subscription ${localSubscriptions[0].id} cancelled`)
      
      // Send cancellation confirmation email
      // In a real app, you'd get the user details and send an email
    }
  } catch (error) {
    console.error('Error cancelling subscription:', error)
  }
}

async function handleSubscriptionUpdated(
  subscription: Stripe.Subscription,
  bookService: any
) {
  console.log('Subscription updated:', subscription.id)
  
  try {
    const localSubscriptions = await bookService.listSubscriptions({
      stripe_subscription_id: subscription.id
    })
    
    if (localSubscriptions.length > 0) {
      const updateData: any = {
        is_active: subscription.status === 'active',
        updated_at: new Date()
      }
      
      if ((subscription as any).current_period_end) {
        updateData.expires_at = new Date((subscription as any).current_period_end * 1000)
      }
      
      await bookService.updateSubscription(localSubscriptions[0].id, updateData)
      console.log(`Subscription ${localSubscriptions[0].id} updated`)
    }
  } catch (error) {
    console.error('Error updating subscription:', error)
  }
}
