import {
  createStep,
  StepResponse,
} from "@medusajs/framework/workflows-sdk"
import {
  ModuleRegistrationName,
} from "@medusajs/framework/utils"

export type MarkFulfillmentShippedStepInput = {
  fulfillment_id: string
  order_id: string
}

export const markFulfillmentShippedStep = createStep(
  "mark-fulfillment-shipped",
  async (
    { fulfillment_id, order_id }: MarkFulfillmentShippedStepInput,
    { container }
  ) => {
    console.log('📦 Marking fulfillment as shipped for digital order')
    console.log('  - Fulfillment ID:', fulfillment_id)
    console.log('  - Order ID:', order_id)

    const fulfillmentModuleService = container.resolve(
      ModuleRegistrationName.FULFILLMENT
    )

    try {

      // Mark fulfillment as shipped (delivered for digital products)
      const updatedFulfillment = await fulfillmentModuleService.updateFulfillment(
        fulfillment_id,
        {
          shipped_at: new Date(),
          data: {
            tracking_number: `DIGITAL_${order_id}_${Date.now()}`,
            tracking_url: null,
            delivery_method: "digital",
            delivered_at: new Date().toISOString(),
            status: "delivered",
          },
        }
      )

      console.log('✅ Fulfillment marked as shipped/delivered')
      console.log('  - Fulfillment ID:', updatedFulfillment.id)
      console.log('  - Shipped At:', updatedFulfillment.shipped_at)

      return new StepResponse({
        fulfillment: updatedFulfillment,
      })
    } catch (error) {
      console.error('❌ Error marking fulfillment as shipped:', error)

      // For digital products, we can continue even if fulfillment update fails
      // The main digital delivery should still work
      // We'll try to get the existing fulfillment to return proper data
      try {
        const existingFulfillment = await fulfillmentModuleService.retrieveFulfillment(
          fulfillment_id
        )

        return new StepResponse({
          fulfillment: existingFulfillment,
        })
      } catch (retrieveError) {
        console.error('❌ Error retrieving fulfillment:', retrieveError)

        // If we can't retrieve the fulfillment either, we need to throw
        // to prevent the workflow from continuing with invalid data
        throw new Error(`Failed to update or retrieve fulfillment ${fulfillment_id}: ${error.message}`)
      }
    }
  }
)

export default markFulfillmentShippedStep
