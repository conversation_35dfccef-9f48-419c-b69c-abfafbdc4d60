import { writable, get } from 'svelte/store'
import type { Cart, CartItem, Product } from '$lib/types/products'
import { currentCurrency, currentRegion } from './currency'
import { apiClient } from '$lib/api/client'
import { isAuthenticated, currentCustomer } from '$lib/stores/auth'
import { browser } from '$app/environment'

// Extended cart interface for MedusaJS integration
interface MedusaCart extends Cart {
  id?: string // MedusaJS cart ID
  customer_id?: string
  region_id?: string
  created_at?: string
  updated_at?: string
}

// Cart store with MedusaJS integration
export const cartStore = writable<MedusaCart>({
  items: [],
  total_amount: 0,
  currency: 'USD', // Will be updated when region changes
  id: undefined,
  customer_id: undefined,
  region_id: undefined
})

// Track cart loading state
export const cartLoading = writable<boolean>(false)

// Track if cart is synced with backend
export const cartSynced = writable<boolean>(false)

// Update cart currency when region changes
currentCurrency.subscribe(currency => {
  cartStore.update(cart => {
    console.log('🛒 Cart currency updated to:', currency)
    cart.currency = currency
    return cart
  })
})

// Helper function to calculate cart total with current currency
function calculateCartTotal(items: CartItem[], discountAmount?: number): number {
  const subtotal = items.reduce((total, item) => {
    return total + (item.product.price * item.quantity)
  }, 0)

  const total = discountAmount ? Math.max(0, subtotal - discountAmount) : subtotal
  return total
}

// Cart functions with authentication integration
export const cartActions = {
  // Initialize cart (called on app startup)
  initialize: async () => {
    cartLoading.set(true)
    try {
      const auth = get(isAuthenticated)
      const customer = get(currentCustomer)

      if (auth && customer) {
        // User is logged in - retrieve their backend cart
        await cartActions.retrieveCustomerCart(customer.id)
      } else {
        // Anonymous user - load from localStorage
        cartActions.loadFromLocalStorage()
      }
    } catch (error) {
      console.error('❌ Failed to initialize cart:', error)
      // Fallback to local storage
      cartActions.loadFromLocalStorage()
    } finally {
      cartLoading.set(false)
    }
  },

  // Retrieve customer's cart from backend (gets existing cart from metadata)
  retrieveCustomerCart: async (customerId: string) => {
    try {
      console.log('🔄 Retrieving customer cart for:', customerId)

      const token = localStorage.getItem('auth_token')
      console.log('🔑 Auth token from localStorage:', token ? `${token.substring(0, 20)}...` : 'NO TOKEN')

      if (!token) {
        console.warn('⚠️ No auth token found, cannot retrieve customer cart')
        cartActions.loadFromLocalStorage()
        return
      }

      // Get customer data to check for existing cart ID in metadata
      console.log('🔄 Calling getCustomer API...')
      const customerResponse = await apiClient.getCustomer(token)
      console.log('👤 Customer API response:', customerResponse.data ? 'SUCCESS' : 'FAILED', customerResponse.error || '')

      if (!customerResponse.data?.customer) {
        console.error('❌ Failed to get customer data')
        cartActions.loadFromLocalStorage()
        return
      }

      const customer = customerResponse.data.customer
      const existingCartId = customer.metadata?.cart_id

      if (existingCartId) {
        // Customer has existing cart - retrieve it
        console.log('🔍 Found existing cart ID in customer metadata:', existingCartId)
        try {
          const cartResponse = await apiClient.getCart(existingCartId)
          if (cartResponse.data?.cart) {
            const customerCart = cartResponse.data.cart
            console.log('✅ Retrieved customer\'s persistent cart:', customerCart.id)

            // Update store with customer's cart
            cartActions.updateFromBackendCart(customerCart)
            cartSynced.set(true)
            console.log('✅ Customer cart retrieved successfully')
            return
          }
        } catch (error) {
          console.warn('⚠️ Failed to retrieve existing cart, will create new one:', error)
        }
      }

      // No existing cart or failed to retrieve - create new one
      console.log('🛒 Creating new persistent cart for customer...')
      const region = get(currentRegion)
      const response = await apiClient.createCart({
        region_id: region?.id
      })

      if (response.data?.cart) {
        const backendCart = response.data.cart
        console.log('🛒 Created new cart:', backendCart.id)

        // Transfer cart to customer
        try {
          const transferResponse = await apiClient.transferCartToCustomer(backendCart.id, token)
          if (transferResponse.data?.cart) {
            const customerCart = transferResponse.data.cart
            console.log('👤 Cart transferred to customer:', customerCart.id)

            // Save cart ID to customer metadata
            await cartActions.saveCartIdToCustomer(customerCart.id, token)

            // Update store with customer's cart
            cartActions.updateFromBackendCart(customerCart)
            cartSynced.set(true)
            console.log('✅ Customer cart created and saved successfully')
          }
        } catch (error) {
          console.warn('⚠️ Failed to transfer cart to customer:', error)
          // Use anonymous cart as fallback
          cartActions.updateFromBackendCart(backendCart)
          cartSynced.set(true)
        }
      } else {
        throw new Error('Failed to create customer cart')
      }
    } catch (error) {
      console.error('❌ Failed to retrieve customer cart:', error)
      cartSynced.set(false)
      // Fallback to local cart
      cartActions.loadFromLocalStorage()
    }
  },

  // Handle user login - retrieve customer's persistent cart and merge guest items
  handleLogin: async (customerId: string) => {
    try {
      console.log('🔄 Handling user login for cart:', customerId)

      const currentCart = get(cartStore)
      const region = get(currentRegion)
      const token = localStorage.getItem('auth_token')

      if (!token) {
        console.warn('⚠️ No auth token found, cannot handle login')
        return
      }

      // Get customer data to check for existing cart ID in metadata
      const customerResponse = await apiClient.getCustomer(token)
      if (!customerResponse.data?.customer) {
        console.error('❌ Failed to get customer data')
        return
      }

      const customer = customerResponse.data.customer
      const existingCartId = customer.metadata?.cart_id

      let customerCart: any = null

      if (existingCartId) {
        // Customer has existing cart - retrieve it
        console.log('🔍 Found existing cart ID in customer metadata:', existingCartId)
        try {
          const cartResponse = await apiClient.getCart(existingCartId)
          if (cartResponse.data?.cart) {
            customerCart = cartResponse.data.cart
            console.log('✅ Retrieved customer\'s persistent cart:', customerCart.id)
          }
        } catch (error) {
          console.warn('⚠️ Failed to retrieve existing cart, will create new one:', error)
        }
      }

      if (!customerCart) {
        // No existing cart or failed to retrieve - create new one
        console.log('🛒 Creating new persistent cart for customer...')
        const response = await apiClient.createCart({
          region_id: region?.id
        })

        if (response.data?.cart) {
          const backendCart = response.data.cart
          console.log('🛒 Created new cart:', backendCart.id)

          // Transfer cart to customer
          try {
            const transferResponse = await apiClient.transferCartToCustomer(backendCart.id, token)
            if (transferResponse.data?.cart) {
              customerCart = transferResponse.data.cart
              console.log('👤 Cart transferred to customer:', customerCart.id)
            }
          } catch (error) {
            console.warn('⚠️ Failed to transfer cart to customer:', error)
            customerCart = backendCart
          }

          // Save cart ID to customer metadata
          await cartActions.saveCartIdToCustomer(customerCart.id, token)
        }
      }

      // If guest has items, merge them with customer's cart
      if (currentCart.items.length > 0 && customerCart) {
        console.log('📦 Guest has items, merging with customer\'s persistent cart...')
        await cartActions.addLocalItemsToBackend(customerCart.id, currentCart.items)

        // Get updated cart after adding items
        const updatedCartResponse = await apiClient.getCart(customerCart.id)
        if (updatedCartResponse.data?.cart) {
          customerCart = updatedCartResponse.data.cart
        }
      }

      if (customerCart) {
        // Update store with customer's cart
        cartActions.updateFromBackendCart(customerCart)
        cartActions.clearLocalStorage()
        cartSynced.set(true)
        console.log('✅ Customer cart loaded successfully')
      }
    } catch (error) {
      console.error('❌ Failed to handle login:', error)
      // Fallback to creating new cart
      await cartActions.retrieveCustomerCart(customerId)
    }
  },

  // Load cart from localStorage
  loadFromLocalStorage: () => {
    try {
      if (!browser) return

      const stored = localStorage.getItem('cart')
      if (stored) {
        const cart = JSON.parse(stored)
        cartStore.set({
          items: cart.items || [],
          total_amount: cart.total_amount || 0,
          currency: cart.currency || get(currentCurrency),
          discount_amount: cart.discount_amount || 0,
          id: undefined,
          customer_id: undefined,
          region_id: undefined
        })
        console.log('🛒 Local cart loaded:', cart.items?.length || 0, 'items')
      } else {
        // Initialize empty cart
        cartStore.set({
          items: [],
          total_amount: 0,
          currency: get(currentCurrency),
          discount_amount: 0
        })
        console.log('🛒 Empty cart initialized')
      }
      cartSynced.set(false)
    } catch (error) {
      console.error('❌ Failed to load local cart:', error)
      // Initialize empty cart on error
      cartStore.set({
        items: [],
        total_amount: 0,
        currency: get(currentCurrency),
        discount_amount: 0
      })
    }
  },

  // Update store from backend cart data
  updateFromBackendCart: (backendCart: any) => {
    cartStore.update(cart => {
      cart.id = backendCart.id
      cart.customer_id = backendCart.customer_id
      cart.region_id = backendCart.region_id
      cart.items = backendCart.items?.map((item: any) => ({
        product_id: item.variant?.product_id || item.product_id,
        product: {
          id: item.variant?.product_id || item.product_id,
          title: item.title,
          price: item.unit_price,
          // Map other fields as needed
        },
        quantity: item.quantity,
        added_at: item.created_at,
        id: item.id
      })) || []
      cart.total_amount = backendCart.total || 0
      cart.currency = backendCart.region?.currency_code || get(currentCurrency)
      cart.created_at = backendCart.created_at
      cart.updated_at = backendCart.updated_at

      console.log("🛒 Cart updated from backend:", cart.items)

      return cart
    })
  },

  // Add local items to backend cart (helper for login)
  addLocalItemsToBackend: async (cartId: string, localItems: CartItem[]) => {
    try {
      console.log('🔄 Adding local items to backend cart...')

      for (const item of localItems) {
        try {
          // Use variant_id from product if available
          const variantId = item.product.variant_id || item.product.id
          if (!variantId) {
            console.warn('⚠️ Skipping item without variant_id:', item.product.title)
            continue
          }

          await apiClient.addToCart(cartId, {
            variant_id: variantId,
            quantity: item.quantity
          })
          console.log('✅ Added to backend cart:', item.product.title)
        } catch (error) {
          console.warn('⚠️ Failed to add item to backend cart:', item.product.title, error)
        }
      }

      // Get updated cart from backend
      const response = await apiClient.getCart(cartId)
      if (response.data?.cart) {
        cartActions.updateFromBackendCart(response.data.cart)
      }

      console.log('✅ Local items added to backend cart')
    } catch (error) {
      console.error('❌ Failed to add local items to backend cart:', error)
    }
  },

  // Clear local storage
  clearLocalStorage: () => {
    if (browser) {
      localStorage.removeItem('cart')
      console.log('🧹 Local cart storage cleared')
    }
  },

  // Save cart ID to customer metadata
  saveCartIdToCustomer: async (cartId: string, token: string) => {
    try {
      console.log('💾 Saving cart ID to customer metadata:', cartId)

      await apiClient.updateCustomer('', {
        metadata: {
          cart_id: cartId
        }
      }, token)

      console.log('✅ Cart ID saved to customer metadata successfully')
    } catch (error) {
      console.error('❌ Failed to save cart ID to customer metadata:', error)
    }
  },

  // Merge local cart items to backend (legacy function)
  mergeLocalToBackend: async (cartId: string, localItems: CartItem[]) => {
    try {
      console.log('🔄 Merging local cart items to backend...')

      for (const item of localItems) {
        try {
          await apiClient.addToCart(cartId, {
            variant_id: item.product.id, // Assuming product ID is variant ID
            quantity: item.quantity
          })
        } catch (error) {
          console.warn('⚠️ Failed to add item to backend cart:', item.product.title, error)
        }
      }

      // Get updated cart from backend
      const response = await apiClient.getCart(cartId)
      if (response.data?.cart) {
        cartActions.updateFromBackendCart(response.data.cart)
      }

      // Clear local storage since items are now in backend
      if (browser) {
        localStorage.removeItem('cart')
      }

      console.log('✅ Local cart merged to backend')
    } catch (error) {
      console.error('❌ Failed to merge local cart:', error)
    }
  },

  // Handle user logout - clear local storage completely
  handleLogout: () => {
    // Clear cart completely
    cartStore.set({
      items: [],
      total_amount: 0,
      currency: get(currentCurrency),
      id: undefined,
      customer_id: undefined,
      region_id: undefined
    })

    // Clear local storage completely
    cartActions.clearLocalStorage()

    cartSynced.set(false)
    console.log('🛒 Cart cleared after logout')
  },
  // Add item to cart (with authentication support)
  addItem: async (product: Product, quantity: number = 1) => {
    const auth = get(isAuthenticated)
    const currentCart = get(cartStore)

    if (auth && currentCart.id) {
      // User is logged in and has backend cart
      await cartActions.addItemToBackend(currentCart.id, product, quantity)
    } else {
      // Anonymous user or no backend cart - use local storage
      cartActions.addItemLocal(product, quantity)
    }
  },

  // Add item to backend cart
  addItemToBackend: async (cartId: string, product: Product, quantity: number) => {
    try {
      cartLoading.set(true)

      // Ensure we have a valid variant ID
      const variantId = product.variant_id
      if (!variantId) {
        throw new Error(`Product ${product.title} does not have a valid variant ID. Cannot add to cart.`)
      }

      const response = await apiClient.addToCart(cartId, {
        variant_id: variantId,
        quantity
      })

      if (response.data?.cart) {
        cartActions.updateFromBackendCart(response.data.cart)
        console.log('🛒 Added to backend cart:', product.title)
      }
    } catch (error) {
      console.error('❌ Failed to add to backend cart:', error)
      // Fallback to local cart
      cartActions.addItemLocal(product, quantity)
    } finally {
      cartLoading.set(false)
    }
  },

  // Add item to local cart
  addItemLocal: (product: Product, quantity: number = 1) => {
    cartStore.update(cart => {
      const existingItemIndex = cart.items.findIndex(item => item.product_id === product.id)

      if (existingItemIndex >= 0) {
        // Update quantity if item already exists
        cart.items[existingItemIndex].quantity += quantity
      } else {
        // Add new item
        const newItem: CartItem = {
          product_id: product.id,
          product,
          quantity,
          added_at: new Date().toISOString()
        }
        cart.items.push(newItem)
      }

      // Recalculate total with current currency
      cart.total_amount = calculateCartTotal(cart.items, cart.discount_amount)
      cart.currency = get(currentCurrency)

      return cart
    })

    // Save to localStorage
    saveCartToStorage()
    console.log('🛒 Added to local cart:', product.title, 'Quantity:', quantity)
  },

  // Remove item from cart
  removeItem: async (productId: string) => {
    const auth = get(isAuthenticated)
    const currentCart = get(cartStore)

    if (auth && currentCart.id) {
      // User is logged in and has backend cart - remove from backend
      await cartActions.removeItemFromBackend(currentCart.id, productId)
    } else {
      // Anonymous user - remove from local cart
      cartActions.removeItemLocal(productId)
    }
  },

  // Remove item from backend cart
  removeItemFromBackend: async (cartId: string, productId: string) => {
    try {
      cartLoading.set(true)

      // Find the line item ID for this product
      const currentCart = get(cartStore)
      const item = currentCart.items.find(item =>
        item.product_id === productId ||
        item.product?.id === productId ||
        item.variant_id === productId
      )

      console.log('🔍 Removing item from backend cart:', item)

      if (!item || !item.id) {
        console.warn('⚠️ Could not find line item ID for product:', productId, item)
        // Fallback to local removal
  //      cartActions.removeItemLocal(productId)
        return
      }

      // Remove line item from backend cart
      const response = await apiClient.removeFromCart(cartId, item.id)
      if (!response.error) {
        cartActions.removeItemLocal(productId)
        console.log('✅ Removed from backend cart:', productId)
      }
    } catch (error) {
      console.error('❌ Failed to remove from backend cart:', error)
      // Fallback to local removal
//      cartActions.removeItemLocal(productId)
    } finally {
      cartLoading.set(false)
    }
  },

  // Remove item from local cart
  removeItemLocal: (productId: string) => {
    cartStore.update(cart => {
      cart.items = cart.items.filter(item => item.product_id !== productId)

      // Recalculate total with current currency
      cart.total_amount = calculateCartTotal(cart.items, cart.discount_amount)
      cart.currency = get(currentCurrency)

      return cart
    })

    saveCartToStorage()
  },

  // Update item quantity
  updateQuantity: (productId: string, quantity: number) => {
    if (quantity <= 0) {
      cartActions.removeItem(productId)
      return
    }
    
    cartStore.update(cart => {
      const itemIndex = cart.items.findIndex(item => item.product_id === productId)
      
      if (itemIndex >= 0) {
        cart.items[itemIndex].quantity = quantity
        
        // Recalculate total with current currency
        cart.total_amount = calculateCartTotal(cart.items, cart.discount_amount)
        cart.currency = get(currentCurrency)
      }
      
      return cart
    })
    
    saveCartToStorage()
  },

  // Clear cart
  clearCart: () => {
    cartStore.set({
      items: [],
      total_amount: 0,
      currency: get(currentCurrency)
    })

    saveCartToStorage()
  },

  // Apply discount code
  applyDiscount: (code: string, amount: number) => {
    cartStore.update(cart => {
      cart.discount_code = code
      cart.discount_amount = amount
      
      // Recalculate total with discount and current currency
      cart.total_amount = calculateCartTotal(cart.items, amount)
      cart.currency = get(currentCurrency)
      
      return cart
    })
    
    saveCartToStorage()
  },

  // Remove discount
  removeDiscount: () => {
    cartStore.update(cart => {
      cart.discount_code = undefined
      cart.discount_amount = undefined
      
      // Recalculate total without discount and with current currency
      cart.total_amount = calculateCartTotal(cart.items)
      cart.currency = get(currentCurrency)
      
      return cart
    })
    
    saveCartToStorage()
  },

  // Update cart currency when region changes
  updateCurrency: () => {
    cartStore.update(cart => {
      cart.currency = get(currentCurrency)
      // Recalculate totals with new currency (prices might be different)
      cart.total_amount = calculateCartTotal(cart.items, cart.discount_amount)
      return cart
    })
    saveCartToStorage()
  }
}

// Helper functions
function saveCartToStorage() {
  if (browser) {
    const cart = get(cartStore)
    // Only save to localStorage if it's not a backend cart
    if (!cart.id) {
      localStorage.setItem('cart', JSON.stringify(cart))
    }
  }
}

// Derived stores
export const cartItemCount = writable(0)
export const cartTotal = writable(0)

cartStore.subscribe(cart => {
  cartItemCount.set(cart.items.reduce((total, item) => total + item.quantity, 0))
  cartTotal.set(cart.total_amount)
})
