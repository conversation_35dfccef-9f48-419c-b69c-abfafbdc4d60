import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk"
import { createDigitalProductOrderStep } from "./steps/create-digital-product-order"
import { sendDigitalOrderNotificationStep } from "./steps/send-digital-order-notification"
import { markFulfillmentShippedStep } from "./steps/mark-fulfillment-shipped"

type WorkflowInput = {
  order_id: string
  fulfillment_id: string
}

const fulfillDigitalOrderWorkflow = createWorkflow(
  "fulfill-digital-order",
  (input: WorkflowInput) => {
    console.log('🚀 Starting digital order fulfillment workflow')
    console.log('  - Order ID:', input.order_id)
    console.log('  - Fulfillment ID:', input.fulfillment_id)

    // Create digital product order record directly
    const { digital_product_order } = createDigitalProductOrderStep({
      order_id: input.order_id,
      digital_products: [], // Will be handled inside the step
    })

    // Send notification to customer
    sendDigitalOrderNotificationStep({
      digital_product_order: digital_product_order,
    })

    // Mark fulfillment as shipped/delivered
    markFulfillmentShippedStep({
      fulfillment_id: input.fulfillment_id,
      order_id: input.order_id,
    })

    console.log('🎯 Digital order fulfillment workflow completed')

    // Return simple response
    return new WorkflowResponse({
      success: true,
      message: "Digital order fulfillment completed successfully",
      order_id: input.order_id,
      fulfillment_id: input.fulfillment_id,
      digital_product_order: digital_product_order,
    })
  }
)

export default fulfillDigitalOrderWorkflow
