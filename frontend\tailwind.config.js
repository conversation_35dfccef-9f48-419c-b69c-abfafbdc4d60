/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        // Navigation and dark text color
        navigation: '#8D6E53',
        // Background and light text color
        background: '#F6F2E9',
        // Blue accent for buttons and titles
        accent: '#62AEC8',

        primary: {
          50: '#f7f4f1',
          100: '#ede6df',
          200: '#dcc9b8',
          300: '#c7a68b',
          400: '#b4876a',
          500: '#8D6E53', // Main navigation color
          600: '#7a5f4a',
          700: '#654f3e',
          800: '#544237',
          900: '#473831',
        },
        secondary: {
          50: '#f0f9fc',
          100: '#daf1f7',
          200: '#b8e3f0',
          300: '#87cee5',
          400: '#62AEC8', // Main accent color
          500: '#4a8ba3',
          600: '#3f7489',
          700: '#365f70',
          800: '#31505d',
          900: '#2c434f',
        },
        neutral: {
          50: '#faf9f7',
          100: '#F6F2E9', // Main background
          200: '#f0ede6',
          300: '#e8e3d8',
          400: '#d4c5b9',
          500: '#b8a898',
          600: '#9d8c7a',
          700: '#7d6b5a',
          800: '#6b5440',
          900: '#5a4735',
        }
      },
      fontFamily: {
        'title': ['Montserrat', 'sans-serif'],
        'body': ['Roboto', 'sans-serif'],
        'sans': ['Roboto', 'sans-serif'],
      },
      fontWeight: {
        'medium': '500',
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}

