import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Mock subscription plans
    const plans = [
      {
        id: "plan_6_months",
        name: "מנוי 6 חודשים",
        name_en: "6 Months Subscription",
        description: "גישה מלאה לכל הפרקים למשך 6 חודשים",
        description_en: "Full access to all chapters for 6 months",
        price: 29.99,
        currency: "USD",
        duration_months: 6,
        features: [
          "גישה לכל הפרקים",
          "תוכן חדש כל שבוע",
          "תמיכה טכנית",
          "מעקב אחר התקדמות"
        ],
        features_en: [
          "Access to all chapters",
          "New content every week", 
          "Technical support",
          "Progress tracking"
        ],
        popular: false,
        savings_percentage: 0
      },
      {
        id: "plan_12_months",
        name: "מנו<PERSON> שנת<PERSON>",
        name_en: "Annual Subscription",
        description: "גישה מלאה לכל הפרקים למשך שנה שלמה עם הנחה",
        description_en: "Full access to all chapters for a full year with discount",
        price: 49.99,
        currency: "USD",
        duration_months: 12,
        features: [
          "גישה לכל הפרקים",
          "תוכן חדש כל שבוע",
          "תמיכה טכנית מועדפת",
          "מעקב אחר התקדמות",
          "גישה לקורסים מתקדמים",
          "הורדת פרקים לצפייה לא מקוונת"
        ],
        features_en: [
          "Access to all chapters",
          "New content every week",
          "Priority technical support", 
          "Progress tracking",
          "Access to advanced courses",
          "Download chapters for offline viewing"
        ],
        popular: true,
        savings_percentage: 17 // Compared to 2x 6-month plans
      }
    ]

    res.json({
      plans,
      message: "Available subscription plans",
      currency_note: "All prices in USD. Local payment methods supported."
    })
  } catch (error) {
    console.error("Error fetching subscription plans:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}
