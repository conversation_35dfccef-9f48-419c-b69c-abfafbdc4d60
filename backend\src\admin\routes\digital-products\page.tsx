import { defineRouteConfig } from "@medusajs/admin-sdk"
import { PhotoSolid, Plus } from "@medusajs/icons"
import {
  Container,
  Heading,
  Table,
  Button,
  Drawer,
  Input,
  Label,
  Select,
  Textarea,
  toast,
} from "@medusajs/ui"
import { useState, useMemo, useEffect } from "react"
import { DigitalProduct, MediaType } from "../../types"

const DigitalProductsPage = () => {
  const [digitalProducts, setDigitalProducts] = useState<
    DigitalProduct[]
  >([])
  const [currentPage, setCurrentPage] = useState(0)
  const pageLimit = 20
  const [count, setCount] = useState(0)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [productVariants, setProductVariants] = useState<any[]>([])
  const [loadingVariants, setLoadingVariants] = useState(false)
  const [createForm, setCreateForm] = useState({
    name: "",
    productVariantId: "",
    medias: [{
      type: MediaType.MAIN,
      url: "",
      file_id: "",
      mime_type: "",
      useDirectUrl: true
    }]
  })

  const pagesCount = useMemo(() => {
    return count / pageLimit
  }, [count])
  const canNextPage = useMemo(
    () => currentPage < pagesCount - 1,
    [currentPage, pagesCount]
  )
  const canPreviousPage = useMemo(
    () => currentPage > 0,
    [currentPage]
  )

  const nextPage = () => {
    if (canNextPage) {
      setCurrentPage((prev) => prev + 1)
    }
  }

  const previousPage = () => {
    if (canPreviousPage) {
      setCurrentPage((prev) => prev - 1)
    }
  }

  const fetchProducts = () => {
    const query = new URLSearchParams({
      limit: `${pageLimit}`,
      offset: `${pageLimit * currentPage}`,
    })

    fetch(`/admin/digital-products?${query.toString()}`, {
      credentials: "include",
    })
    .then((res) => res.json())
    .then(({
      digital_products: data,
      count,
    }) => {
      setDigitalProducts(data)
      setCount(count)
    })
  }

  // Fetch product variants when drawer opens
  useEffect(() => {
    if (isCreateOpen) {
      console.log('Create drawer opened, fetching product variants...')
      fetchProductVariants()
    }
  }, [isCreateOpen])

  const fetchProductVariants = async () => {
    setLoadingVariants(true)
    try {
      // Use the original API endpoint that was working
      const response = await fetch(`/admin/product-variants?fields=id,title,sku,product_id,*product&limit=100`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      if (response.ok) {
        const data = await response.json()
        console.log('Fetched product variants response:', data)
        setProductVariants(data.variants || [])
      } else {
        console.error('Failed to fetch product variants, status:', response.status)
        const errorText = await response.text()
        console.error('Error response:', errorText)

        // If unauthorized, the user might not be logged in
        if (response.status === 401) {
          toast.error('Please log in to load product variants')
        } else {
          toast.error('Failed to load product variants')
        }
      }
    } catch (error) {
      console.error('Error fetching product variants:', error)
      toast.error('Failed to load product variants')
    } finally {
      setLoadingVariants(false)
    }
  }

  const handleCreateProduct = async () => {
    // Validation
    if (!createForm.name || !createForm.productVariantId) {
      toast.error("Please fill in all required fields")
      return
    }

    if (createForm.medias.some(media =>
      media.useDirectUrl ? !media.url : (!media.file_id || !media.mime_type)
    )) {
      toast.error("Please provide content for all media files")
      return
    }

    setIsCreating(true)
    try {
      const response = await fetch('/admin/digital-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: createForm.name,
          product_variant_id: createForm.productVariantId,
          medias: createForm.medias.map(media => ({
            type: media.type,
            file_id: media.useDirectUrl ? undefined : media.file_id,
            mime_type: media.useDirectUrl ? undefined : media.mime_type,
            url: media.useDirectUrl ? media.url : undefined,
            useDirectUrl: media.useDirectUrl,
          })),
        }),
      })

      if (response.ok) {
        toast.success("Digital product created successfully!")
        setIsCreateOpen(false)
        setCreateForm({
          name: "",
          productVariantId: "",
          medias: [{
            type: MediaType.MAIN,
            url: "",
            file_id: "",
            mime_type: "",
            useDirectUrl: true
          }]
        })
        fetchProducts()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to create digital product")
      }
    } catch (error) {
      toast.error("Failed to create digital product")
    } finally {
      setIsCreating(false)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [currentPage])

  return (
    <Container>
      <div className="flex justify-between items-center mb-4">
        <Heading level="h2">Digital Products</Heading>
        <Button onClick={() => setIsCreateOpen(true)}>
          <Plus className="mr-2" />
          Create Digital Product
        </Button>
      </div>
      <Table>
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell>Name</Table.HeaderCell>
            <Table.HeaderCell>Action</Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {digitalProducts.map((digitalProduct) => (
            <Table.Row key={digitalProduct.id}>
              <Table.Cell>
                {digitalProduct.name}
              </Table.Cell>
              <Table.Cell>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    if (digitalProduct.product_variant?.product_id) {
                      window.open(`/app/products/${digitalProduct.product_variant.product_id}`, '_blank')
                    } else {
                      toast.error("Product not found")
                    }
                  }}
                >
                  View Product
                </Button>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
      <Table.Pagination
        count={count}
        pageSize={pageLimit}
        pageIndex={currentPage}
        pageCount={pagesCount}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        previousPage={previousPage}
        nextPage={nextPage}
      />

      <Drawer>
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Create Digital Product</Drawer.Title>
            <Drawer.Description>
              Create a digital product structure and link it to a regular Medusa product.
              You'll set pricing through the Products section after creation.
            </Drawer.Description>
          </Drawer.Header>
          <Drawer.Body className="space-y-4 max-h-[70vh] overflow-y-auto">
            <div>
              <Label htmlFor="name">Digital Product Name *</Label>
              <Input
                id="name"
                value={createForm.name}
                onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter digital product name"
                required
              />
            </div>

            <div>
              <Label htmlFor="productVariant">Product Variant *</Label>
              {loadingVariants ? (
                <div className="flex items-center gap-2 p-2 text-gray-500">
                  <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-gray-600 rounded-full"></div>
                  Loading product variants...
                </div>
              ) : (
                <select
                  id="productVariant"
                  value={createForm.productVariantId}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, productVariantId: e.target.value }))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select a product variant...</option>
                  {loadingVariants ? (
                    <option disabled>Loading variants...</option>
                  ) : productVariants.length === 0 ? (
                    <option disabled>No product variants found</option>
                  ) : (
                    productVariants.map((variant) => {
                      console.log('Rendering variant:', variant)
                      return (
                        <option key={variant.id} value={variant.id}>
                          {variant.product?.title || 'Unknown Product'} - {variant.title || 'Default Variant'}
                          {variant.sku && ` (${variant.sku})`}
                        </option>
                      )
                    })
                  )}
                </select>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Select the product variant this digital content will be linked to
              </p>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>💡 Pricing Note:</strong> Product pricing will be managed through the regular Products section after creation.
                This creates the digital content structure and links it to a product that you can price normally.
              </p>
            </div>

            <div>
              <Label>Digital Content</Label>
              <p className="text-sm text-gray-600 mb-2">
                Add URLs for digital content (like FlipHTML5 links) or upload files.
              </p>
              {createForm.medias.map((media, index) => (
                <div key={index} className="border p-4 rounded space-y-3">
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <Label className="text-sm">Media Type</Label>
                      <select
                        value={media.type}
                        onChange={(e) => {
                          const newMedias = [...createForm.medias]
                          newMedias[index].type = e.target.value as MediaType
                          setCreateForm(prev => ({ ...prev, medias: newMedias }))
                        }}
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select media type</option>
                        <option value={MediaType.MAIN}>📚 Main Content</option>
                        <option value={MediaType.PREVIEW}>👁️ Preview</option>
                      </select>
                    </div>

                    <div className="flex-1">
                      <Label className="text-sm">Content Source</Label>
                      <select
                        value={media.useDirectUrl ? "url" : "upload"}
                        onChange={(e) => {
                          const newMedias = [...createForm.medias]
                          newMedias[index].useDirectUrl = e.target.value === "url"
                          setCreateForm(prev => ({ ...prev, medias: newMedias }))
                        }}
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="url">🔗 Direct URL</option>
                        <option value="upload">📁 File Upload</option>
                      </select>
                    </div>
                  </div>

                  {media.useDirectUrl ? (
                    <div>
                      <Label className="text-sm">Content URL</Label>
                      <Input
                        placeholder="https://online.fliphtml5.com/... or any direct URL"
                        value={media.url}
                        onChange={(e) => {
                          const newMedias = [...createForm.medias]
                          newMedias[index].url = e.target.value
                          setCreateForm(prev => ({ ...prev, medias: newMedias }))
                        }}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        For FlipHTML5: Copy the iframe src URL (e.g., https://online.fliphtml5.com/xrcwd/qlyk/)
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div>
                        <Label className="text-sm">File ID</Label>
                        <Input
                          placeholder="File ID from upload API"
                          value={media.file_id}
                          onChange={(e) => {
                            const newMedias = [...createForm.medias]
                            newMedias[index].file_id = e.target.value
                            setCreateForm(prev => ({ ...prev, medias: newMedias }))
                          }}
                        />
                      </div>

                      <div>
                        <Label className="text-sm">MIME Type</Label>
                        <Input
                          placeholder="e.g., application/pdf, video/mp4"
                          value={media.mime_type}
                          onChange={(e) => {
                            const newMedias = [...createForm.medias]
                            newMedias[index].mime_type = e.target.value
                            setCreateForm(prev => ({ ...prev, medias: newMedias }))
                          }}
                        />
                      </div>
                    </div>
                  )}

                  {createForm.medias.length > 1 && (
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        const newMedias = createForm.medias.filter((_, i) => i !== index)
                        setCreateForm(prev => ({ ...prev, medias: newMedias }))
                      }}
                    >
                      🗑️ Remove
                    </Button>
                  )}
                </div>
              ))}

              <Button
                variant="secondary"
                onClick={() => {
                  setCreateForm(prev => ({
                    ...prev,
                    medias: [...prev.medias, {
                      type: MediaType.MAIN,
                      url: "",
                      file_id: "",
                      mime_type: "",
                      useDirectUrl: true
                    }]
                  }))
                }}
              >
                Add Media File
              </Button>
            </div>
          </Drawer.Body>
          <Drawer.Footer>
            <Button
              onClick={handleCreateProduct}
              disabled={isCreating || !createForm.name || !createForm.productVariantId}
            >
              {isCreating ? "Creating..." : "Create Digital Product"}
            </Button>
            <Button
              variant="secondary"
              onClick={() => setIsCreateOpen(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>
    </Container>
  )
}

export const config = defineRouteConfig({
  label: "Digital Products",
  icon: PhotoSolid,
})

export default DigitalProductsPage