<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { authStore, isAuthenticated, currentCustomer } from '$lib/stores/auth'
  import { apiClient } from '$lib/api/client'
  import BookReader from '$lib/components/BookReader.svelte'

  import { _ } from '$lib/i18n'
  import { getCurrentLocale, isRTL } from '$lib/i18n'

  let currentLocale = 'en'
  let rtl = false
  
  // Loading states
  let isLoadingBooks = false
  let isLoadingSettings = false
  let booksError = ''
  let settingsError = ''
  
  // Data
  let purchasedBooks: any[] = []
  let sampleBookUrl = ''
  let currentBook: any = null // Currently displayed book in iframe
  let currentBookTitle = ''

  $: currentLocale = getCurrentLocale()
  $: rtl = isRTL(currentLocale)
  
  // Redirect to home if user logs out
  $: if (!$isAuthenticated && typeof window !== 'undefined') {
    goto('/')
  }

  onMount(async () => {
    // Initialize auth state from localStorage with token validation
    await authStore.initialize()

    // Redirect to home if not authenticated
    if (!$isAuthenticated) {
      goto('/')
      return
    }

    // Load purchased books and settings
    loadPurchasedBooks()
    loadSampleBookSettings()
  })

  async function loadPurchasedBooks() {
    if (!$currentCustomer) return

    try {
      isLoadingBooks = true
      booksError = ''

      const token = localStorage.getItem('auth_token')
      if (!token) {
        booksError = 'Authentication token not found. Please sign in again.'
        return
      }

      // Get customer's digital products using direct fetch
      try {
        const response = await fetch('http://localhost:9000/store/customers/me/digital-products', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'x-publishable-api-key': 'pk_a00302b727fcf4f5285975080843c4bb959bc257a8a7e070ea0967c396c64505',
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          purchasedBooks = data.digital_products || []
          console.log('📚 Loaded digital products:', purchasedBooks)
        } else {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
          booksError = errorData.error || `HTTP ${response.status}: ${response.statusText}`
          console.error('❌ API Error:', response.status, errorData)
        }
      } catch (fetchError) {
        console.error('❌ Network Error:', fetchError)
        booksError = 'Network error: Unable to connect to server'
      }
    } catch (error) {
      console.error('Failed to load purchased books:', error)
      booksError = 'Failed to load your library. Please try again.'
    } finally {
      isLoadingBooks = false
    }
  }

  async function loadSampleBookSettings() {
    try {
      isLoadingSettings = true
      settingsError = ''

      console.log('🔍 Loading sample book settings...')

      // Get store settings for sample book URL
      const response = await apiClient.getStoreSettings()
      console.log('📡 Store settings response:', response)

      if (response.data?.settings?.sample_book_url) {
        sampleBookUrl = response.data.settings.sample_book_url
        console.log('📖 Sample book URL found:', sampleBookUrl)
      } else {
        console.log('⚠️ No sample book URL found in settings')
        // For testing, set a default URL if none configured
        sampleBookUrl = 'https://online.fliphtml5.com/xrcwd/qlyk/'
        console.log('📖 Using default sample book URL:', sampleBookUrl)
      }
    } catch (error) {
      console.error('❌ Failed to load sample book settings:', error)
      settingsError = 'Failed to load sample book settings.'
      // For testing, set a default URL even on error
      sampleBookUrl = 'https://online.fliphtml5.com/xrcwd/qlyk/'
      console.log('📖 Using fallback sample book URL:', sampleBookUrl)
    } finally {
      isLoadingSettings = false
    }
  }

  function loadBookInReader(book: any) {
    // Find the main content URL from digital medias
    const mainMedia = book.medias?.find((media: any) => media.type === 'main')

    if (mainMedia?.url) {
      currentBook = {
        ...book,
        fliphtml5_url: mainMedia.url // Keep for backward compatibility
      }
      currentBookTitle = book.name || book.title
      console.log('📖 Loading book in reader:', currentBookTitle, mainMedia.url)

      // Log the detected reader type
      const readerType = detectReaderType(currentBook)
      console.log('📄 Detected reader type:', readerType)
    } else {
      alert($_('library.no_reading_link'))
      console.warn('⚠️ No main media URL found for book:', book)
    }
  }

  function detectReaderType(book: any): 'pdf' | 'fliphtml5' | 'none' {
    if (!book) return 'none'

    const mainMedia = book.medias?.find((media: any) => media.type === 'main')
    const url = mainMedia?.url || book.fliphtml5_url

    if (!url) return 'none'

    // Check if it's a PDF file
    if (url.toLowerCase().includes('.pdf') || url.toLowerCase().includes('pdf')) {
      return 'pdf'
    }

    // Check if it's a FlipHTML5 URL
    if (url.includes('fliphtml5.com') || url.includes('online.fliphtml5')) {
      return 'fliphtml5'
    }

    // Default to FlipHTML5 for backward compatibility
    return 'fliphtml5'
  }

  function loadSampleInReader() {
    if (sampleBookUrl) {
      currentBook = {
        id: 'sample',
        title: $_('library.sample_book'),
        fliphtml5_url: sampleBookUrl
      }
      currentBookTitle = $_('library.sample_book')
    } else {
      alert($_('library.sample_not_available'))
    }
  }

  // Initialize with sample book when data is loaded
  $: if (sampleBookUrl && !currentBook) {
    loadSampleInReader()
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString(currentLocale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
</script>

<svelte:head>
  <title>{$_('library.page_title')} - Hebrew Book Store</title>
  <meta name="description" content={$_('library.page_description')} />
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    {#if $isAuthenticated && $currentCustomer}
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="font-book-title text-3xl md:text-4xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('library.my_library')}
        </h1>
        <p class="font-book-text text-lg" style="color: var(--color-text-secondary);">
          {$_('library.access_purchased_books')}
        </p>
      </div>

      <!-- Book Reader Section -->
      <div class="mb-8">
        <div class="card-classic p-6">
          <!-- Reader Header -->
          <div class="flex items-center justify-between mb-4">
            <div>
              <h2 class="font-book-title text-xl font-bold mb-2" style="color: var(--color-text-primary);">
                {currentBookTitle || $_('library.sample_book')}
              </h2>
              <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
                {#if currentBook?.id === 'sample'}
                  {$_('library.try_sample_description')}
                {:else if currentBook}
                  {$_('library.reading_purchased_book')}
                {:else}
                  {$_('library.loading_reader')}
                {/if}
              </p>
            </div>
            <button
              on:click={loadSampleInReader}
              disabled={!sampleBookUrl || isLoadingSettings}
              class="btn-classic-small"
              class:opacity-50={!sampleBookUrl || isLoadingSettings}
            >
              {#if isLoadingSettings}
                <div class="flex items-center">
                  <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                  {$_('library.loading')}
                </div>
              {:else}
                📖 {$_('library.load_sample')}
              {/if}
            </button>
          </div>

          <!-- Enhanced Book Reader (PDF.js + FlipHTML5) -->
          <BookReader
            book={currentBook}
            title={currentBookTitle}
            height="600px"
            isLoading={isLoadingSettings}
          />
        </div>
      </div>

      <!-- Purchased Books Section -->
      <div class="mb-12">
        <h2 class="font-book-title text-2xl font-bold mb-6" style="color: var(--color-text-primary);">
          {$_('library.purchased_books')}
        </h2>

        {#if isLoadingBooks}
          <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
            <p class="font-book-text" style="color: var(--color-text-secondary);">
              {$_('library.loading_books')}
            </p>
          </div>
        {:else if booksError}
          <div class="card-classic p-6 text-center">
            <div class="text-4xl mb-4">❌</div>
            <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
              {$_('library.error_loading_books')}
            </h3>
            <p class="font-book-text text-sm mb-4" style="color: var(--color-text-secondary);">
              {booksError}
            </p>
            <button on:click={loadPurchasedBooks} class="btn-classic-small">
              {$_('library.retry')}
            </button>
          </div>
        {:else if purchasedBooks.length === 0}
          <div class="card-classic p-8 text-center">
            <div class="text-6xl mb-4">📚</div>
            <h3 class="font-book-title text-xl font-semibold mb-2" style="color: var(--color-text-primary);">
              {$_('library.no_books')}
            </h3>
            <p class="font-book-text mb-6" style="color: var(--color-text-secondary);">
              {$_('library.no_books_description')}
            </p>
            <a href="/catalog" class="btn-classic">
              {$_('library.browse_books')}
            </a>
          </div>
        {:else}
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {#each purchasedBooks as book (book.id)}
              <div class="card-classic p-6 transition-all duration-300 hover:shadow-lg">
                <!-- Book Icon/Cover -->
                <div class="text-center mb-4">
                  <div class="text-4xl mb-2">
                    {#if book.type?.toLowerCase() === 'book'}
                      📚
                    {:else if book.type?.toLowerCase() === 'chapter'}
                      📖
                    {:else}
                      📄
                    {/if}
                  </div>
                  <div class="text-xs font-book-text uppercase tracking-wide" style="color: var(--color-text-accent);">
                    {book.type || 'Book'}
                  </div>
                </div>

                <!-- Book Details -->
                <div class="text-center mb-4">
                  <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
                    {book.name || book.title}
                  </h3>
                  {#if book.description}
                    <p class="font-book-text text-sm mb-2" style="color: var(--color-text-secondary);">
                      {book.description.length > 100 ? book.description.substring(0, 100) + '...' : book.description}
                    </p>
                  {/if}
                  <p class="font-book-text text-xs" style="color: var(--color-text-accent);">
                    {$_('library.purchased')}: {formatDate(book.purchased_at || book.purchaseDate)}
                  </p>
                </div>

                <!-- Read Button -->
                <div class="text-center">
                  <button
                    on:click={() => loadBookInReader(book)}
                    disabled={!book.medias?.some((m: any) => m.type === 'main') && !book.fliphtml5_url}
                    class="w-full px-4 py-2 rounded-lg font-book-text font-semibold text-sm transition-all duration-300"
                    class:opacity-50={!book.medias?.some((m: any) => m.type === 'main') && !book.fliphtml5_url}
                    style="background: var(--color-primary); color: white;"
                  >
                    {#if book.medias?.some((m: any) => m.type === 'main') || book.fliphtml5_url}
                      📖 {$_('library.read_now')}
                    {:else}
                      📖 {$_('library.coming_soon')}
                    {/if}
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>

    {:else}
      <!-- Not Authenticated -->
      <div class="text-center py-16">
        <div class="text-6xl mb-6">🔒</div>
        <h1 class="font-book-title text-2xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('library.access_required')}
        </h1>
        <p class="font-book-text text-lg mb-8" style="color: var(--color-text-secondary);">
          {$_('library.please_sign_in')}
        </p>
        <a href="/" class="btn-classic inline-block">
          {$_('library.go_home')}
        </a>
      </div>
    {/if}
  </div>
</div>


