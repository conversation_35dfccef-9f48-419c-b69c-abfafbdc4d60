import {
  createWorkflow,
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk"
import {
  completeCartWorkflow,
  createRemoteLinkStep,
  createOrderFulfillmentWorkflow,
  emitEventStep,
} from "@medusajs/medusa/core-flows"
import {
  Modules,
} from "@medusajs/framework/utils"
import createDigitalProductOrderStep from "./steps/create-digital-product-order"
import { DIGITAL_PRODUCT_MODULE } from "../../modules/digital-product"

type WorkflowInput = {
  cart_id: string
}

const createDigitalProductOrderWorkflow = createWorkflow(
  "create-digital-product-order",
  (input: WorkflowInput) => {
    const { id } = completeCartWorkflow.runAsStep({
      input: {
        id: input.cart_id,
      },
    })

    // Create digital product order directly without complex queries
    const { digital_product_order } = createDigitalProductOrderStep({
      order_id: id, // Pass order ID for lookup
    })

    createRemoteLinkStep([{
      [DIGITAL_PRODUCT_MODULE]: {
        digital_product_order_id: digital_product_order.id,
      },
      [Modules.ORDER]: {
        order_id: id,
      },
    }])

    // Create fulfillment for digital items
    createOrderFulfillmentWorkflow.runAsStep({
      input: {
        order_id: id,
        items: [], // Will be determined by the fulfillment workflow
      },
    })

    emitEventStep({
      eventName: "digital_product_order.created",
      data: {
        id: digital_product_order.id,
      },
    })

    return new WorkflowResponse({
      order_id: id,
      digital_product_order,
      success: true,
    })
  }
)

export default createDigitalProductOrderWorkflow