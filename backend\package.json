{"name": "hebrew-book-store-new", "version": "0.0.1", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "predeploy": "npx medusa db:migrate", "postinstall": "medusa build", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "dependencies": {"@medusajs/admin-sdk": "2.8.4", "@medusajs/cli": "2.8.4", "@medusajs/framework": "2.8.4", "@medusajs/medusa": "2.8.4", "@medusajs/ui": "^4.0.15", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/node-cron": "^3.0.11", "awilix": "^8.0.1", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "node-cron": "^4.1.0", "node-mailjet": "^6.0.9", "pg": "^8.13.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "stripe": "^18.2.1"}, "devDependencies": {"@medusajs/test-utils": "2.8.4", "@mikro-orm/cli": "6.4.3", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.13", "@types/node": "^20.0.0", "@types/node-mailjet": "^3.3.12", "jest": "^29.7.0", "prop-types": "^15.8.1", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11", "yalc": "^1.0.0-pre.53"}, "engines": {"node": ">=20"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}