import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  // Set CORS headers
    console.log('OPTIONS request for ', req, "setting headers")
  res.setHeader('Access-Control-Allow-Origin', process.env.STORE_CORS || 'http://localhost:5173')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  res.setHeader('Access-Control-Allow-Credentials', 'true')

  res.json({
    status: "OK",
    message: "Hebrew Book Store API is running",
    timestamp: new Date().toISOString(),
    version: "1.0.0"
  })
}

export async function OPTIONS(req: MedusaRequest, res: MedusaResponse) {
  // Set CORS headers for preflight request
  console.log('OPTIONS request for ', req, "setting headers")
  res.setHeader('Access-Control-Allow-Origin', process.env.STORE_CORS || 'http://localhost:5173')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
  res.setHeader('Access-Control-Allow-Credentials', 'true')
  res.setHeader('Access-Control-Max-Age', '86400') // 24 hours
  
  res.status(200).end()
}
