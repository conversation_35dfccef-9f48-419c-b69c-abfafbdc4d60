import { writable, get } from 'svelte/store'
import type { Product } from '$lib/types/products'
import { apiClient } from '$lib/api/client'
import { formatPrice } from '$lib/utils/currency'

// Products store
export const productsStore = writable<Product[]>([])
export const isLoadingProducts = writable<boolean>(false)
export const productsError = writable<string | null>(null)

// Currency store for formatting prices
export const currentCurrency = writable<string>('USD')
export const currentRegion = writable<any>(null)

// Helper function to convert Medusa product to our Product type
function convertMedusaProduct(medusaProduct: any): Product {
  const variant = medusaProduct.variants?.[0]

  // Use MedusaJS v2 calculated_price instead of old prices array
  const priceAmount = variant?.calculated_price?.calculated_amount || 0
  const currencyCode = variant?.calculated_price?.currency_code || get(currentCurrency)

  // Format the price with proper currency
  const formattedPrice = formatPrice(priceAmount, currencyCode)

  // Debug: Check if product has variants
  if (!variant) {
    console.warn(`⚠️ Product ${medusaProduct.title} (${medusaProduct.id}) has no variants. This may cause cart issues.`)
  }



  const converted = {
    id: medusaProduct.id,
    variant_id: variant?.id, // Store variant ID for cart operations (only if variant exists)
    type: medusaProduct.type?.value?.toLowerCase() || medusaProduct.metadata?.product_type || 'chapter',
    title: medusaProduct.title,
    description: medusaProduct.description || '',
    short_description: medusaProduct.subtitle || '',
    preview_content: medusaProduct.metadata?.preview_content || 'Preview content not available.',
    price: priceAmount, // Medusa stores prices in cents, and we also will, only show in UI as main currency unit
    formatted_price: formattedPrice, // Formatted price with currency
    original_price: medusaProduct.metadata?.original_price ? Number(medusaProduct.metadata.original_price) : undefined,
    currency: currencyCode,
    difficulty_level: medusaProduct.metadata?.difficulty_level || 'beginner',
    access_type: medusaProduct.metadata?.access_type || (medusaProduct.type?.value?.toLowerCase() === 'subscription' ? 'subscription' : 'permanent'),
    duration: medusaProduct.metadata?.duration || (medusaProduct.type?.value?.toLowerCase() === 'subscription' ? '6m' : undefined),
    tags: medusaProduct.tags?.map((tag: any) => tag.value) || [],
    topics: medusaProduct.metadata?.topics ? JSON.parse(medusaProduct.metadata.topics) : [medusaProduct.type?.value || 'General'],
    language: medusaProduct.metadata?.language || 'en',
    reading_time_minutes: medusaProduct.metadata?.reading_time_minutes ? Number(medusaProduct.metadata.reading_time_minutes) : 0,
    order_in_book: medusaProduct.metadata?.order_in_book ? Number(medusaProduct.metadata.order_in_book) : undefined,
    is_published: true, // Store API only returns published products
    is_featured: medusaProduct.metadata?.is_featured === 'true' || false,
    created_at: medusaProduct.created_at,
    updated_at: medusaProduct.updated_at,
    cover_image: medusaProduct.thumbnail || null,
    video_url: medusaProduct.metadata?.video_url || null,
    audio_url: medusaProduct.metadata?.audio_url || null,
    related_products: medusaProduct.metadata?.related_products ? JSON.parse(medusaProduct.metadata.related_products) : [],
    bundle_items: medusaProduct.metadata?.bundle_items ? JSON.parse(medusaProduct.metadata.bundle_items) : [],
    prerequisites: medusaProduct.metadata?.prerequisites ? JSON.parse(medusaProduct.metadata.prerequisites) : [],
    stats: {
      views: medusaProduct.metadata?.views ? Number(medusaProduct.metadata.views) : Math.floor(Math.random() * 100) + 50, // Random demo views
      purchases: medusaProduct.metadata?.purchases ? Number(medusaProduct.metadata.purchases) : Math.floor(Math.random() * 20) + 5, // Random demo purchases
      rating: medusaProduct.metadata?.rating ? Number(medusaProduct.metadata.rating) : 4.5, // Default good rating
      reviews_count: medusaProduct.metadata?.reviews_count ? Number(medusaProduct.metadata.reviews_count) : Math.floor(Math.random() * 15) + 3 // Random demo reviews
    },
    seo: {
      meta_title: medusaProduct.metadata?.meta_title || medusaProduct.title,
      meta_description: medusaProduct.metadata?.meta_description || medusaProduct.description || medusaProduct.subtitle || '',
      keywords: medusaProduct.metadata?.keywords ? JSON.parse(medusaProduct.metadata.keywords) : [medusaProduct.type?.value?.toLowerCase() || 'product'],
      slug: medusaProduct.handle || medusaProduct.title?.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
    }
  }

  return converted
}

// Note: Product creation, updating, and deletion is handled through
// the official Medusa.js admin panel at http://localhost:9000/app
// This store only handles reading and displaying products from the API

// Products actions
export const productActions = {
  // Load all products from API
  loadProducts: async (forceRegionId?: string) => {
    isLoadingProducts.set(true)
    productsError.set(null)

    try {
      let regionId = forceRegionId

      // If no region is forced, check what regions are available and use current or first
      if (!regionId) {
        console.log('🌍 Fetching available regions...')
        const regionsResponse = await apiClient.getRegions()

        if (regionsResponse.data && regionsResponse.data.regions) {
          console.log('🌍 Available regions:', regionsResponse.data.regions)

          // Check if we have a current region set
          let currentRegionValue: any = null
          currentRegion.subscribe(value => currentRegionValue = value)()

          if (currentRegionValue && currentRegionValue.id) {
            regionId = currentRegionValue.id
            console.log('🌍 Using current region:', currentRegionValue.name, '(', regionId, ')')
          } else {
            // Use the first available region for pricing
            const firstRegion = regionsResponse.data.regions[0]
            if (firstRegion) {
              regionId = firstRegion.id
              console.log('🌍 Using first available region:', firstRegion.name, '(', regionId, ')')

              // Set the current region and currency
              currentRegion.set(firstRegion)
              if (firstRegion.currency_code) {
                currentCurrency.set(firstRegion.currency_code.toUpperCase())
                console.log('💰 Using currency:', firstRegion.currency_code.toUpperCase())
              }
            }
          }
        }
      }

      const response = await apiClient.getProducts({
        limit: 100,
        region_id: regionId
      })

      if (response.data && response.data.products) {
        console.log("📦 Products response received, count:", response.data.products.length);
        const products = response.data.products.map(convertMedusaProduct)

        // Step 2: Load metadata separately and merge it
        console.log('🔄 Loading product metadata...')
        await productActions.loadAndMergeMetadata(products)

        productsStore.set(products)
        console.log('✅ Products loaded from Medusa.js API:', products.length)
      } else {
        throw new Error(response.error || 'Failed to load products from API')
      }
    } catch (error) {
      console.error('❌ Failed to load products from Medusa.js API:', error)
      productsError.set(error instanceof Error ? error.message : 'Failed to load products')
      productsStore.set([]) // No fallback - empty array
    } finally {
      isLoadingProducts.set(false)
    }
  },

  // Load metadata separately and merge into existing products
  async loadAndMergeMetadata(products: Product[]): Promise<void> {
    try {
      // Get product IDs for metadata request
      const productIds = products.map(p => p.id)
      console.log('📋 Fetching metadata for', productIds.length, 'products')

      // Request metadata for all products
      const metadataResponse = await apiClient.getProductsMetadata(productIds)

      if (metadataResponse.data && metadataResponse.data.products) {
        console.log('📋 Metadata response received for', metadataResponse.data.products.length, 'products')

        // Create a map of product ID to metadata for quick lookup
        const metadataMap = new Map()
        metadataResponse.data.products.forEach((product: any) => {
          if (product.metadata) {
            metadataMap.set(product.id, product.metadata)
            console.log('📋 Product', product.id, 'has metadata:', Object.keys(product.metadata))
          }
        })

        // Merge metadata into existing products
        products.forEach(product => {
          const metadata = metadataMap.get(product.id)
          if (metadata) {
            // Add metadata to product (update the Product interface to support this)
            (product as any).metadata = metadata
            console.log('✅ Merged metadata for product:', product.title)
          }
        })

        console.log('✅ Metadata loading and merging completed')
      } else {
        console.log('⚠️ No metadata response received')
      }
    } catch (error) {
      console.error('❌ Failed to load metadata:', error)
      // Don't throw - metadata is optional, main products should still work
    }
  },

  // Load products from a collection (for table of contents)
  async loadCollectionProducts(collectionId: string, regionId?: string): Promise<Product[]> {
    try {
      console.log('📚 Loading products from collection:', collectionId)

      const response = await apiClient.getCollectionProducts(collectionId, {
        limit: 100, // Get all chapters
        region_id: regionId
      })

      if (response.data && response.data.products) {
        console.log('📚 Collection products received:', response.data.products.length)

        // Convert to our Product type
        const products = response.data.products.map(convertMedusaProduct)

        // Load metadata for collection products too
        await productActions.loadAndMergeMetadata(products)

        console.log('✅ Collection products loaded and processed:', products.length)
        return products
      } else {
        console.log('⚠️ No products found in collection:', collectionId)
        return []
      }
    } catch (error) {
      console.error('❌ Failed to load collection products:', error)
      return []
    }
  },

  // Note: Product CRUD operations (create, update, delete) are handled
  // through the official Medusa.js admin panel at http://localhost:9000/app
  // This store only provides read-only access to products for the storefront

  // Get product by ID
  getProductById: async (productId: string): Promise<Product | null> => {
    try {
      const response = await apiClient.getProduct(productId)
      if (response.data && response.data.product) {
        return convertMedusaProduct(response.data.product)
      }
      return null
    } catch (error) {
      console.error('Failed to get product:', error)
      return null
    }
  },

  // Get published products only
  getPublishedProducts: async (): Promise<Product[]> => {
    try {
      const response = await apiClient.getProducts({ limit: 100 })
      if (response.data && response.data.products) {
        return response.data.products
          .filter(p => p.status === 'published')
          .map(convertMedusaProduct)
      }
      return []
    } catch (error) {
      console.error('Failed to get published products:', error)
      return []
    }
  },

  // Get featured products
  getFeaturedProducts: async (): Promise<Product[]> => {
    try {
      const response = await apiClient.getProducts({ limit: 100 })
      if (response.data && response.data.products) {
        return response.data.products
          .filter(p => p.status === 'published')
          .map(convertMedusaProduct)
      }
      return []
    } catch (error) {
      console.error('Failed to get featured products:', error)
      return []
    }
  },

  // Search products
  searchProducts: async (query: string): Promise<Product[]> => {
    try {
      const response = await apiClient.searchProducts(query, { limit: 100 })
      if (response.data && response.data.hits) {
        return response.data.hits
          .filter(p => p.status === 'published')
          .map(convertMedusaProduct)
      }
      return []
    } catch (error) {
      console.error('❌ Failed to search products via API:', error)
      return []
    }
  },

  // Filter products (client-side for now, could be moved to API)
  filterProducts: async (filters: {
    types?: string[]
    difficulties?: string[]
    topics?: string[]
    priceRange?: { min: number; max: number }
    featured?: boolean
  }): Promise<Product[]> => {
    const allProducts = await new Promise<Product[]>((resolve) => {
      productsStore.subscribe(products => resolve(products))()
    })

    let filtered = allProducts.filter(p => p.is_published)

    if (filters.types && filters.types.length > 0) {
      filtered = filtered.filter(p => filters.types!.includes(p.type))
    }

    if (filters.difficulties && filters.difficulties.length > 0) {
      filtered = filtered.filter(p => filters.difficulties!.includes(p.difficulty_level))
    }

    if (filters.topics && filters.topics.length > 0) {
      filtered = filtered.filter(p =>
        p.topics.some(topic => filters.topics!.includes(topic))
      )
    }

    if (filters.priceRange) {
      filtered = filtered.filter(p =>
        p.price >= filters.priceRange!.min && p.price <= filters.priceRange!.max
      )
    }

    if (filters.featured) {
      filtered = filtered.filter(p => p.is_featured)
    }

    return filtered
  },

  // Reload products with a specific region
  reloadWithRegion: async (regionId: string) => {
    console.log('🔄 Reloading products with region:', regionId)
    await productActions.loadProducts(regionId)
  }
}
