<script lang="ts">
	import { page } from '$app/stores'
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL, setLocale } from '$lib/i18n'
	import { goto } from '$app/navigation'
	import { isAuthenticated, currentCustomer, authStore } from '$lib/stores/auth'
	import CartIcon from './CartIcon.svelte'
	import Cart from './Cart.svelte'
	import RegionSelector from './RegionSelector.svelte'
	import AuthModal from './AuthModal.svelte'

	let currentLocale = 'en'
	let rtl = false
	let mobileMenuOpen = false
	let cartOpen = false
	let authModalOpen = false
	let authMode: 'login' | 'register' = 'login'

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)
	$: currentPath = $page.url.pathname

	function handleLanguageChange(newLocale: string) {
		setLocale(newLocale)
	}

	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen
	}

	function closeMobileMenu() {
		mobileMenuOpen = false
	}

	function openCart() {
		cartOpen = true
	}

	function closeCart() {
		cartOpen = false
	}

	function openAuthModal(mode: 'login' | 'register') {
		authMode = mode
		authModalOpen = true
	}

	function closeAuthModal() {
		authModalOpen = false
	}

	function handleAuthSuccess() {
		authModalOpen = false
		// Could show a success message here
	}

	async function handleLogout() {
		await authStore.logout()
		// Could show a logout message here
	}

	function isActive(path: string): boolean {
		return currentPath === path || (path !== '/' && currentPath.startsWith(path))
	}

	const navItems = [
		{ path: '/', label: 'navigation.home' },
		{ path: '/catalog', label: 'navigation.catalog' },
		{ path: '/contact', label: 'navigation.contact' }
	]
</script>

<nav class="bg-white shadow-lg sticky top-0 z-50" class:rtl>
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="flex justify-between items-center h-16">
			<!-- Logo -->
			<div class="flex items-center">
				<button 
					on:click={() => goto('/')}
					class="text-xl font-bold text-blue-600 hover:text-blue-800 transition-colors"
				>
					{$_('home.title')}
				</button>
			</div>

			<!-- Desktop Navigation -->
			<div class="hidden md:flex items-center space-x-8" class:space-x-reverse={rtl}>
				{#each navItems as item}
					<a
						href={item.path}
						class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
						class:text-blue-600={isActive(item.path)}
						class:bg-blue-50={isActive(item.path)}
						class:text-gray-600={!isActive(item.path)}
						class:hover:text-blue-600={!isActive(item.path)}
						class:hover:bg-gray-50={!isActive(item.path)}
						on:click={closeMobileMenu}
					>
						{$_(item.label)}
					</a>
				{/each}
			</div>

			<!-- Authentication, Region Selector, Language Switcher, Cart & Mobile Menu Button -->
			<div class="flex items-center space-x-4" class:space-x-reverse={rtl}>
				<!-- Authentication -->
				{#if $isAuthenticated}
					<!-- User Menu -->
					<div class="hidden md:flex items-center space-x-2" class:space-x-reverse={rtl}>
						<a
							href="/account"
							class="text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors"
							title={$_('account.my_account')}
						>
							👤 {$currentCustomer?.first_name || $_('account.my_account')}
						</a>
						<button
							on:click={handleLogout}
							class="text-sm font-medium text-gray-600 hover:text-red-600 transition-colors"
							title={$_('auth.sign_out')}
						>
							{$_('auth.sign_out')}
						</button>
					</div>
				{:else}
					<!-- Login/Register Buttons -->
					<div class="hidden md:flex items-center space-x-2" class:space-x-reverse={rtl}>
						<button
							on:click={() => openAuthModal('login')}
							class="text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors"
						>
							{$_('auth.sign_in')}
						</button>
						<button
							on:click={() => openAuthModal('register')}
							class="text-sm font-medium px-3 py-1 rounded-md border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white transition-colors"
						>
							{$_('auth.create_account')}
						</button>
					</div>
				{/if}

				<!-- Cart Icon -->
				<CartIcon on:open={openCart} />

				<!-- Region Selector -->
				<RegionSelector />

				<!-- Language Switcher -->
				<div class="flex items-center space-x-2" class:space-x-reverse={rtl}>
					<label for="nav-language-select" class="sr-only">
						{$_('common.language')}
					</label>
					<select
						id="nav-language-select"
						class="block w-auto px-2 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
						value={currentLocale}
						on:change={(e) => handleLanguageChange((e.target as HTMLSelectElement).value)}
					>
						<option value="en">EN</option>
						<option value="he">עב</option>
						<option value="ru">РУ</option>
					</select>
				</div>

				<!-- Mobile menu button -->
				<button
					type="button"
					class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
					on:click={toggleMobileMenu}
					aria-expanded={mobileMenuOpen}
				>
					<span class="sr-only">Open main menu</span>
					{#if !mobileMenuOpen}
						<svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					{:else}
						<svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					{/if}
				</button>
			</div>
		</div>

		<!-- Mobile Navigation Menu -->
		{#if mobileMenuOpen}
			<div class="md:hidden">
				<div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
					{#each navItems as item}
						<a
							href={item.path}
							class="block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
							class:text-blue-600={isActive(item.path)}
							class:bg-blue-50={isActive(item.path)}
							class:text-gray-600={!isActive(item.path)}
							class:hover:text-blue-600={!isActive(item.path)}
							class:hover:bg-gray-50={!isActive(item.path)}
							class:text-right={rtl}
							on:click={closeMobileMenu}
						>
							{$_(item.label)}
						</a>
					{/each}

					<!-- Mobile Authentication -->
					<div class="px-3 py-2 border-t border-gray-200">
						{#if $isAuthenticated}
							<div class="space-y-2">
								<a
									href="/account"
									class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors"
									class:text-right={rtl}
									on:click={closeMobileMenu}
								>
									👤 {$currentCustomer?.first_name || $_('account.account')}
								</a>
								<button
									on:click={() => { handleLogout(); closeMobileMenu(); }}
									class="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-red-600 hover:bg-gray-50 transition-colors"
									class:text-right={rtl}
								>
									{$_('auth.sign_out')}
								</button>
							</div>
						{:else}
							<div class="space-y-2">
								<button
									on:click={() => { openAuthModal('login'); closeMobileMenu(); }}
									class="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors"
									class:text-right={rtl}
								>
									{$_('auth.sign_in')}
								</button>
								<button
									on:click={() => { openAuthModal('register'); closeMobileMenu(); }}
									class="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors"
									class:text-right={rtl}
								>
									{$_('auth.create_account')}
								</button>
							</div>
						{/if}
					</div>

					<!-- Mobile Region Selector -->
					<div class="px-3 py-2 border-t border-gray-200">
						<RegionSelector />
					</div>
				</div>
			</div>
		{/if}
	</div>
</nav>

<!-- Cart Component -->
<Cart bind:isOpen={cartOpen} on:close={closeCart} />

<!-- Authentication Modal -->
<AuthModal bind:isOpen={authModalOpen} bind:mode={authMode} on:close={closeAuthModal} on:success={handleAuthSuccess} />

<style>
	.rtl {
		direction: rtl;
	}
</style>
