#!/usr/bin/env node

/**
 * <PERSON>ript to check existing users and invites
 * Usage: node scripts/check-users.js [email]
 */

require('dotenv').config()
const { createMedusaContainer } = require('@medusajs/framework')
const { ContainerRegistrationKeys } = require('@medusajs/framework/utils')

async function checkUsers(emailFilter) {
  try {
    console.log('🔍 Checking users and invites...')
    
    const container = createMedusaContainer()
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    
    // Check users
    console.log('\n👥 USERS:')
    const { data: users } = await query.graph({
      entity: "user",
      fields: ["id", "email", "first_name", "last_name", "created_at"],
      ...(emailFilter && { filters: { email: emailFilter } })
    })
    
    if (users && users.length > 0) {
      users.forEach(user => {
        console.log(`  📧 ${user.email} (ID: ${user.id})`)
        console.log(`     Name: ${user.first_name || 'N/A'} ${user.last_name || 'N/A'}`)
        console.log(`     Created: ${user.created_at}`)
        console.log('')
      })
    } else {
      console.log('  No users found')
    }
    
    // Check invites
    console.log('\n📨 INVITES:')
    const { data: invites } = await query.graph({
      entity: "invite",
      fields: ["id", "email", "accepted", "expires_at", "created_at"],
      ...(emailFilter && { filters: { email: emailFilter } })
    })
    
    if (invites && invites.length > 0) {
      invites.forEach(invite => {
        console.log(`  📧 ${invite.email} (ID: ${invite.id})`)
        console.log(`     Accepted: ${invite.accepted ? 'YES' : 'NO'}`)
        console.log(`     Expires: ${invite.expires_at}`)
        console.log(`     Created: ${invite.created_at}`)
        console.log('')
      })
    } else {
      console.log('  No invites found')
    }
    
    // Check auth identities
    console.log('\n🔐 AUTH IDENTITIES:')
    try {
      const { data: identities } = await query.graph({
        entity: "auth_identity",
        fields: ["id", "provider_identities", "created_at"],
        ...(emailFilter && { 
          filters: { 
            provider_identities: { 
              entity_id: emailFilter 
            } 
          } 
        })
      })
      
      if (identities && identities.length > 0) {
        identities.forEach(identity => {
          console.log(`  🆔 ID: ${identity.id}`)
          console.log(`     Provider Identities: ${JSON.stringify(identity.provider_identities, null, 2)}`)
          console.log(`     Created: ${identity.created_at}`)
          console.log('')
        })
      } else {
        console.log('  No auth identities found')
      }
    } catch (error) {
      console.log('  Could not fetch auth identities:', error.message)
    }
    
  } catch (error) {
    console.error('❌ Error checking users:', error)
  }
}

// Get email filter from command line
const emailFilter = process.argv[2]
if (emailFilter) {
  console.log(`🔍 Filtering by email: ${emailFilter}`)
}

checkUsers(emailFilter)
