# 🎛️ **Admin Panel Digital Shipping Setup - Step by Step**
*Verified with actual MedusaJS v2 admin interface*

## 🎯 **Overview**
This guide walks you through setting up digital shipping options using the MedusaJS admin panel interface.

---

## 📋 **Step 1: Create a Digital Shipping Profile**

### **Navigation**:
1. Go to **Settings** (gear icon in sidebar)
2. Click **"Locations and Shipping"**
3. Find **"Shipping Profiles"** section

### **Create Profile**:
1. Click **"Create Shipping Profile"** button
2. **Configure**:
   ```
   Name: Digital Products
   Type: default
   ```
3. Click **"Save"**

### **Result**: 
✅ You now have a "Digital Products" shipping profile

---

## 📋 **Step 2: Create Service Zone** *(Optional but Recommended)*

### **Navigation**:
1. Go to **Settings** → **Locations and Shipping**
2. Click **"Locations"** tab/section
3. Select or create your **stock location** (usually "Main Warehouse" or similar)

### **Create/Edit Fulfillment **:
1. Go to **"Fulfillment Providers"** tab
2. Click **"Connect Fulfillment Provider"** or edit existing one
3. **Configure**:
   ```
   Name: Global Digital Delivery
   Type: shipping
   ```

Connect Sales channels (default)???

### **Add Service Zone**: 
1. Enable Shipping and there you can click **"Add Service Zone"**
2. **Configure**:
   ```
   Name: Global Digital Zone
   ```

### **Add Geo Zones**:
1. Click **"Add Geo Zone"**
2. **Configure**:
   ```
   Type: country
   Countries: Select all countries where you deliver digitally
   (or select major markets like US, EU, etc.)
   ```
3. Click **"Save"**

### **Result**: 
✅ You have a service zone for digital delivery

---

## 📋 **Step 3: Create Digital Shipping Option**

### **Navigation**:
1. Go to **Settings** → **Locations and Shipping**
2. Find **"Shipping Options"** section

### **Create Shipping Option**:
1. Click **"Create Shipping Option"** button
2. **Configure**:
   ```
   Name: Digital Delivery
   Provider: digital-fulfillment
   Shipping Profile: Digital Products
   Service Zone: Global Digital Zone (from Step 2)
   Price Type: Flat Rate
   ```

### **Set Pricing**:
1. In the **"Prices"** section:
   ```
   Currency: USD (or your store currency)
   Amount: 0
   ```
2. Add additional currencies if needed
3. Click **"Save"**

### **Result**: 
✅ You have a digital shipping option with free delivery

---

## 📋 **Step 4: Link Products to Shipping Profile**

### **For Each Digital Product**:

#### **Navigation**:
1. Go to **Products** (in main sidebar)
2. Click on your **digital product**
3. Click **"Edit"** button

#### **Configure Shipping**:
1. Scroll down to find **"Shipping"** section
   - *Note: May be called "Fulfillment" or in an "Advanced" section*
2. Find **"Shipping Profile"** dropdown
3. Select **"Digital Products"**
4. Click **"Save"**

#### **Repeat**:
- Do this for **all digital products**

### **Result**: 
✅ All digital products are linked to the digital shipping profile

---

## ✅ **Verification Steps**

### **Check Shipping Options**:
1. Go to **Settings** → **Locations and Shipping** → **Shipping Options**
2. **Verify**: You see "Digital Delivery" with provider "digital-fulfillment"

### **Check Product Links**:
1. Go to **Products** → [Digital Product] → **Edit**
2. **Verify**: Shipping Profile shows "Digital Products"

### **Test in Storefront**:
1. Add a digital product to cart
2. Go to checkout
3. **Verify**: "Digital Delivery" appears as shipping option
4. **Verify**: Price shows as 0 (free)

---

## 🚨 **Troubleshooting**

### **Can't Find Sections**:
- **Shipping Profiles**: Look in Settings → Locations and Shipping, may be in tabs
- **Service Zones**: Under Locations → [Location] → Fulfillment Sets
- **Product Shipping**: Products → [Product] → Edit, scroll down

### **No "digital-fulfillment" Provider**:
1. Check `medusa-config.ts` has the provider registered
2. Restart backend server
3. Refresh admin panel

### **Shipping Option Not Appearing in Checkout**:
1. Verify product is linked to "Digital Products" profile
2. Check that shipping option has correct service zone
3. Ensure pricing is set for your store currency

---

## 📊 **Visual Checklist**

After completing all steps, you should have:

### **In Shipping Profiles**:
- ✅ "Digital Products" profile exists

### **In Shipping Options**:
- ✅ "Digital Delivery" option exists
- ✅ Provider: "digital-fulfillment"
- ✅ Profile: "Digital Products"
- ✅ Price: 0 in your currency

### **In Products**:
- ✅ Each digital product has "Digital Products" shipping profile

### **In Service Zones** (if created):
- ✅ "Global Digital Zone" with appropriate countries

---

## 🎯 **Expected Behavior After Setup**

### **For New Orders**:
1. Customer adds digital product to cart
2. At checkout, sees "Digital Delivery (Free)" option
3. Completes order
4. Order automatically gets fulfilled
5. Customer receives download links via email
6. Order status shows as "fulfilled"

### **In Admin Panel**:
- New orders with digital products show fulfillments
- Fulfillment provider shows as "digital-fulfillment"
- Order status progresses from "pending" to "fulfilled"

---

## 💡 **Pro Tips**

### **Naming Conventions**:
- **Profile**: "Digital Products" (clear and descriptive)
- **Option**: "Digital Delivery" or "Instant Download"
- **Zone**: "Global Digital Zone" or "Worldwide Digital"

### **Multiple Currencies**:
- Add pricing for each currency your store supports
- Always set amount to 0 for digital delivery

### **Service Zones**:
- Create broad zones (e.g., "Global") for digital products
- No need for complex geographic restrictions

### **Testing**:
- Always test with a real order after setup
- Check both frontend checkout and admin fulfillment
- Verify email notifications are sent

---

## 🔄 **Maintenance**

### **Adding New Digital Products**:
1. Create the product normally
2. **Don't forget**: Set shipping profile to "Digital Products"

### **Updating Pricing**:
- Digital delivery should always be free (0 cost)
- Update currency pricing as needed

### **Monitoring**:
- Check that new digital orders are fulfilled automatically
- Monitor email delivery of download links
- Review fulfillment logs for any errors

---

This guide should give you the exact steps needed to configure digital shipping through the admin panel!
