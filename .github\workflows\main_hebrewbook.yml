# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - hebrewbook

on:
  push:
    branches:
      - main
    paths:
      - 'frontend/**'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'yarn'
          cache-dependency-path: frontend/yarn.lock

      - name: Install dependencies and build
        run: |
          cd frontend
          # Remove package-lock.json to avoid yarn/npm conflicts
          rm -f package-lock.json
          yarn install --frozen-lockfile
          # Sync SvelteKit to generate required files
          yarn svelte-kit sync
          yarn build
          # Copy web.config to build directory
          cp web.config build/
          # Copy package.json and yarn.lock to build directory
          cp package.json build/
          cp yarn.lock build/
          cd build
          # Install all dependencies (Azure will use yarn)
          yarn install --production --frozen-lockfile
          # Update package.json to have correct start script
          node -e "
          const fs = require('fs');
          const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
          pkg.scripts = { start: 'node index.js' };
          pkg.main = 'index.js';
          fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
          "
        env:
          VITE_BACKEND_URL: ${{ secrets.VITE_BACKEND_URL }}
          VITE_MEDUSA_PUBLISHABLE_API_KEY: ${{ secrets.VITE_MEDUSA_PUBLISHABLE_API_KEY }}
          VITE_DEFAULT_REGION_ID: ${{ secrets.VITE_DEFAULT_REGION_ID }}
          VITE_APP_NAME: ${{ secrets.VITE_APP_NAME }}
          VITE_APP_URL: ${{ secrets.VITE_APP_URL }}
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.VITE_STRIPE_PUBLISHABLE_KEY }}
          NODE_ENV: production

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: frontend/build/

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app
      
      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_9CADE47A02A04D5E8BA10530B9DA8AA6 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_2B0FC263885F4578A88F427B50FD9154 }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_EE273AF7256D4EF2AC47CC558200A07E }}

      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'hebrewbook'
          slot-name: 'Production'
          package: .
          