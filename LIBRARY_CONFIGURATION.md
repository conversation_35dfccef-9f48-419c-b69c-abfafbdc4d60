# Library Configuration Guide

This guide explains how to configure the digital library features in your Hebrew Book Store, including setting up FlipHTML5 integration and sample book configuration.

## Overview

The library system allows customers to:
- Read purchased books and chapters online using FlipHTML5 embeds
- Access a sample book for demonstration purposes
- View their complete digital library organized by purchase date

## Configuration Steps

### 1. Sample Book Configuration

The sample book URL is configured through the MedusaJS admin panel using store settings.

#### Steps to Configure Sample Book:

1. **Access MedusaJS Admin Panel**
   - Navigate to your MedusaJS admin dashboard
   - Log in with admin credentials

2. **Navigate to Settings**
   - Go to `Settings` → `Store Details`
   - Or access the store configuration section

3. **Add Sample Book URL**
   - In the store metadata section, add a new field:
     - **Key**: `sample_book_url`
     - **Value**: Your FlipHTML5 embed URL (e.g., `https://online.fliphtml5.com/your-book-id/`)

4. **Save Configuration**
   - Save the store settings
   - The sample book will now be available on the library page

#### Example Configuration:
```json
{
  "sample_book_url": "https://online.fliphtml5.com/xrcwd/qlyk/"
}
```

#### Step-by-Step Sample Book Setup:

1. **Get FlipHTML5 URL**
   - Upload your sample book to FlipHTML5
   - Get the embed code from FlipHTML5 dashboard
   - Extract the `src` URL: `https://online.fliphtml5.com/xrcwd/qlyk/`

2. **Configure in MedusaJS Admin**
   - Access your MedusaJS admin panel
   - Navigate to Settings → Store Details
   - Add metadata field:
     - Key: `sample_book_url`
     - Value: `https://online.fliphtml5.com/xrcwd/qlyk/`
   - Save the configuration

3. **Test Sample Book**
   - Visit your storefront `/library` page
   - Click "Open Sample Book" button
   - Verify the FlipHTML5 book opens in the reader modal

**Note**: If the `/public/settings` endpoint is not available, the system will use a fallback URL for testing. Contact your developer to implement the custom settings endpoint.

### 2. Individual Book FlipHTML5 Configuration

Each book/chapter product can have its own FlipHTML5 reading link configured through product metadata.

#### Steps to Configure Book Reading Links:

1. **Access Product Management**
   - Navigate to `Products` in the MedusaJS admin
   - Select the book/chapter you want to configure

2. **Edit Product Metadata**
   - In the product details, find the metadata section
   - Add a new metadata field:
     - **Key**: `fliphtml5_url`
     - **Value**: The FlipHTML5 embed URL for this specific book

3. **Save Product**
   - Save the product changes
   - The book will now be readable in the customer library

#### Example Product Metadata:
```json
{
  "fliphtml5_url": "https://online.fliphtml5.com/book123/chapter1/",
  "chapters_collection_id": "col_123456789",
  "product_type": "book"
}
```

### 3. FlipHTML5 URL Format

FlipHTML5 URLs should be extracted from the iframe embed code provided by FlipHTML5.

#### Getting FlipHTML5 URLs:

1. **Upload Book to FlipHTML5**
   - Create account on FlipHTML5.com
   - Upload your PDF book/chapter
   - Publish the flipbook

2. **Get Embed Code**
   - In FlipHTML5 dashboard, select your book
   - Go to "Share" → "Embed" options
   - Copy the embed code (HTML iframe)

3. **Extract URL from Embed Code**

   FlipHTML5 provides embed code like this:
   ```html
   <div style="position:relative;padding-top:max(60%,324px);width:100%;height:0;">
     <iframe style="position:absolute;border:none;width:100%;height:100%;left:0;top:0;"
             src="https://online.fliphtml5.com/xrcwd/qlyk/"
             seamless="seamless" scrolling="no" frameborder="0"
             allowtransparency="true" allowfullscreen="true">
     </iframe>
   </div>
   ```

   **Extract only the `src` URL**: `https://online.fliphtml5.com/xrcwd/qlyk/`

4. **Configure in MedusaJS**
   - Use only the extracted URL (not the full HTML)
   - Format: `https://online.fliphtml5.com/[book-id]/[chapter-id]/`
   - Test the URL works in an iframe

#### URL Format Examples:
- ✅ **Correct**: `https://online.fliphtml5.com/xrcwd/qlyk/`
- ✅ **Correct**: `https://online.fliphtml5.com/abcde/fghij/`
- ❌ **Incorrect**: Full HTML embed code
- ❌ **Incorrect**: URLs with additional parameters

## Library Features

### Customer Library Page (`/library`)

The library page provides:

1. **Embedded FlipHTML5 Reader**
   - Always-visible iframe reader (600px height)
   - Shows sample book by default
   - Switches to purchased books when selected
   - Professional reading experience without popups

2. **Reader Controls**
   - "Load Sample" button to return to demo book
   - Current book title display
   - Loading states and error handling

3. **Purchased Books Grid**
   - Shows all purchased books and chapters below the reader
   - Displays purchase date and book details
   - "Read Now" buttons load books in the embedded reader
   - "Coming Soon" for books without reading links

### Navigation Integration

The library is accessible through:
- **Account Dashboard**: "My Library" card
- **Direct URL**: `/library`
- **Authentication Required**: Redirects to home if not logged in

## Technical Implementation

### API Endpoints Used

1. **Store Settings**: `GET /public/settings`
   - Retrieves sample book URL from store metadata

2. **Customer Orders**: `GET /store/orders` (authenticated)
   - Fetches customer's completed orders
   - Filters for book/chapter products
   - Includes product metadata with FlipHTML5 URLs

### Data Flow

1. **Authentication Check**
   - Verify customer is logged in
   - Redirect to home if not authenticated

2. **Load Sample Book**
   - Fetch store settings
   - Extract `sample_book_url` from metadata

3. **Load Purchased Books**
   - Fetch customer orders with authentication
   - Filter completed orders for book/chapter products
   - Extract FlipHTML5 URLs from product metadata

4. **Display Library**
   - Load sample book in embedded reader by default
   - Display purchased books grid below reader
   - Enable book switching in embedded reader

### User Experience Flow

#### Reading Experience:
1. **Visit Library** → `/library` page with embedded reader
2. **Default View** → Sample book loads automatically in reader
3. **Browse Books** → Scroll down to see purchased books grid
4. **Select Book** → Click "Read Now" to load in embedded reader
5. **Switch Books** → Click "Load Sample" to return to demo book
6. **Continuous Reading** → No popups or new windows, all in one page

#### Key Benefits:
- **Always-visible reader** → No modal popups or new tabs
- **Seamless switching** → Easy to try sample and switch to purchased books
- **Professional layout** → Reader integrated into page design
- **Mobile friendly** → Responsive design works on all devices

## Troubleshooting

### Common Issues

1. **Sample Book Not Showing**
   - Check store metadata has `sample_book_url` field
   - Verify URL is accessible and valid
   - Check browser console for errors

2. **Books Not Readable**
   - Verify product metadata has `fliphtml5_url` field
   - Test FlipHTML5 URL works in browser
   - Check iframe embedding is allowed

3. **Library Empty**
   - Verify customer has completed orders
   - Check orders contain book/chapter products
   - Ensure product types are correctly set

4. **Authentication Issues**
   - Check customer is logged in
   - Verify auth token is valid
   - Check API permissions

### Testing

1. **Test Sample Book**
   - Configure sample URL in admin
   - Visit `/library` page
   - Click "Open Sample Book" button

2. **Test Book Reading**
   - Purchase a book with FlipHTML5 URL
   - Visit library page
   - Click "Read Now" on purchased book

3. **Test Error Handling**
   - Try with invalid URLs
   - Test without authentication
   - Verify error messages display correctly

## Security Considerations

1. **Authentication Required**
   - Library access requires valid customer login
   - Orders are filtered by authenticated customer

2. **URL Validation**
   - FlipHTML5 URLs should be validated
   - Consider allowlisting trusted domains

3. **Iframe Security**
   - FlipHTML5 embeds use iframe sandboxing
   - Content is served from FlipHTML5 domain

## Future Enhancements

Potential improvements:
- Download options for offline reading
- Reading progress tracking
- Bookmarks and notes
- Search within library
- Categories and filtering
- Reading statistics

## Backend Implementation

### Custom Settings Endpoint

The library system requires a custom `/public/settings` endpoint to retrieve store configuration. Here's the implementation needed:

#### API Endpoint: `GET /public/settings`

```javascript
// Example implementation for MedusaJS v2
export async function GET(request: Request) {
  try {
    // Get store from MedusaJS
    const storeService = container.resolve("storeService")
    const store = await storeService.retrieve()

    return new Response(JSON.stringify({
      data: {
        settings: store.metadata || {}
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({
      error: 'Failed to load store settings'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
```

#### Expected Response Format:

```json
{
  "data": {
    "settings": {
      "sample_book_url": "https://online.fliphtml5.com/xrcwd/qlyk/",
      "main_video_url": "https://www.youtube.com/embed/your-video",
      "other_config": "values"
    }
  }
}
```

### Alternative: Use Existing Store API

If you prefer not to create a custom endpoint, you can modify the library code to use the existing store API:

```javascript
// In library page, replace getStoreSettings() with:
const response = await apiClient.request('/store')
if (response.data?.store?.metadata?.sample_book_url) {
  sampleBookUrl = response.data.store.metadata.sample_book_url
}
```

## Support

For technical support:
- Check MedusaJS documentation for admin configuration
- Verify FlipHTML5 embed requirements
- Test URLs in isolation before configuring
- Check browser console for JavaScript errors
- Implement the `/public/settings` endpoint or use alternative store API
