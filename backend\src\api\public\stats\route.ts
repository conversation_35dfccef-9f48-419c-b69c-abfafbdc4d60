import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Mock statistics data
    const stats = {
      platform: {
        total_chapters: 25,
        total_users: 1247,
        total_reading_hours: 8934,
        languages_supported: ["עברית", "English", "Русский"],
        difficulty_levels: ["beginner", "intermediate", "advanced"]
      },
      popular_chapters: [
        {
          id: "chapter_1",
          title: "פרק ראשון - מבוא לעברית",
          views: 2341,
          completion_rate: 89,
          average_rating: 4.8
        },
        {
          id: "chapter_2", 
          title: "פרק שני - מילים בסיסיות",
          views: 1876,
          completion_rate: 76,
          average_rating: 4.6
        },
        {
          id: "chapter_3",
          title: "פרק שלישי - משפטים ראשונים",
          views: 1432,
          completion_rate: 68,
          average_rating: 4.7
        }
      ],
      learning_progress: {
        beginner_students: 67,
        intermediate_students: 28,
        advanced_students: 5
      },
      recent_activity: [
        {
          type: "new_chapter",
          title: "פרק חדש: שיחות יומיומיות",
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          type: "milestone",
          title: "1000 משתמשים רשומים!",
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          type: "feature",
          title: "הוספת תמיכה בהורדת פרקים",
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        }
      ],
      testimonials: [
        {
          id: 1,
          name: "שרה כהן",
          text: "הפלטפורמה הזו שינתה לי את הדרך ללמוד עברית. התוכן מעולה והמבנה ברור מאוד!",
          rating: 5,
          course_completed: "מתחילים"
        },
        {
          id: 2,
          name: "David Miller",
          text: "Amazing platform for learning Hebrew! The content is well-structured and engaging.",
          rating: 5,
          course_completed: "Intermediate"
        },
        {
          id: 3,
          name: "Анна Петрова",
          text: "Отличная платформа для изучения иврита. Очень помогает структурированный подход.",
          rating: 4,
          course_completed: "Beginner"
        }
      ]
    }

    res.json({
      stats,
      generated_at: new Date().toISOString(),
      message: "Platform statistics and analytics"
    })
  } catch (error) {
    console.error("Error fetching stats:", error)
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message
    })
  }
}
