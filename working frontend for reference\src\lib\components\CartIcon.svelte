<script lang="ts">
	import { cartItemCount } from '$lib/stores/cart'
	import { createEventDispatcher } from 'svelte'

	const dispatch = createEventDispatcher()
	
	$: itemCount = $cartItemCount

	function openCart() {
		dispatch('open')
	}
</script>

<button
	type="button"
	class="relative p-2 text-gray-600 hover:text-blue-600 transition-colors duration-200"
	on:click={openCart}
	aria-label="Open shopping cart"
>
	<!-- Cart icon -->
	<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
		<path 
			stroke-linecap="round" 
			stroke-linejoin="round" 
			stroke-width="2" 
			d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" 
		/>
	</svg>
	
	<!-- Item count badge -->
	{#if itemCount > 0}
		<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
			{itemCount > 99 ? '99+' : itemCount}
		</span>
	{/if}
</button>
