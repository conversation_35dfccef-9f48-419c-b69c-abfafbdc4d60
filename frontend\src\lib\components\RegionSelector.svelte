<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { getCurrentLocale, isRTL } from '$lib/i18n'
  import { currentRegion, currentCurrency } from '$lib/stores/currency'
  import { apiClient } from '$lib/api/client'
  import { productActions } from '$lib/stores/products'
  import { cartActions } from '$lib/stores/cart'

  let currentLocale = 'en'
  let rtl = false
  let availableRegions: any[] = []
  let selectedRegionId = ''
  let isLoading = false
  let isOpen = false

  $: currentLocale = getCurrentLocale()
  $: rtl = isRTL(currentLocale)
  $: selectedRegion = $currentRegion
  $: currency = $currentCurrency

  onMount(async () => {
    await loadRegions()
  })

  async function loadRegions() {
    try {
      const response = await apiClient.getRegions()
      if (response.data && response.data.regions) {
        availableRegions = response.data.regions
        console.log('🌍 Loaded regions for selector:', availableRegions)
        
        // Set current selection
        if (selectedRegion) {
          selectedRegionId = selectedRegion.id
        } else if (availableRegions.length > 0) {
          selectedRegionId = availableRegions[0].id
        }
      }
    } catch (error) {
      console.error('Failed to load regions:', error)
    }
  }

  async function selectRegion(regionId: string) {
    const region = availableRegions.find(r => r.id === regionId)
    if (!region) return

    isLoading = true
    selectedRegionId = regionId
    
    try {
      // Update the current region and currency
      currentRegion.set(region)
      if (region.currency_code) {
        currentCurrency.set(region.currency_code.toUpperCase())
      }
      
      console.log('🌍 Selected region:', region.name, '(', region.id, ')')
      console.log('💰 Currency changed to:', region.currency_code?.toUpperCase())
      
      // Reload products with new region
      await productActions.loadProducts()

      // Update cart currency
      cartActions.updateCurrency()

      isOpen = false
    } catch (error) {
      console.error('Failed to switch region:', error)
    } finally {
      isLoading = false
    }
  }

  function toggleDropdown() {
    isOpen = !isOpen
  }

  function closeDropdown() {
    isOpen = false
  }

  // Close dropdown when clicking outside
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as Element
    if (!target.closest('.region-selector')) {
      closeDropdown()
    }
  }

  onMount(() => {
    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  })

  function getRegionDisplayName(region: any): string {
    // You can customize this based on your region data structure
    return region.name || region.id
  }

  function getCurrencySymbol(currencyCode: string): string {
    const symbols: Record<string, string> = {
      USD: '$',
      EUR: '€',
      GBP: '£',
      ILS: '₪',
      JPY: '¥',
      CAD: 'C$',
      AUD: 'A$'
    }
    return symbols[currencyCode.toUpperCase()] || currencyCode.toUpperCase()
  }
</script>

<div class="region-selector relative inline-block text-left">
  <button
    type="button"
    class="inline-flex items-center justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
    class:opacity-50={isLoading}
    disabled={isLoading}
    on:click={toggleDropdown}
    aria-haspopup="true"
    aria-expanded={isOpen}
  >
    <div class="flex items-center space-x-2" class:space-x-reverse={rtl}>
      <!-- Globe icon -->
      <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      
      {#if selectedRegion}
        <span class="text-sm">
          {getRegionDisplayName(selectedRegion)}
        </span>
        <span class="text-xs text-gray-500 font-mono">
          {getCurrencySymbol(selectedRegion.currency_code || 'USD')}
        </span>
      {:else}
        <span class="text-sm text-gray-500">
          {$_('region.select_region')}
        </span>
      {/if}
      
      {#if isLoading}
        <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
      {:else}
        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
        </svg>
      {/if}
    </div>
  </button>

  {#if isOpen}
    <div 
      class="origin-top-right absolute {rtl ? 'left-0' : 'right-0'} mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
      role="menu"
      aria-orientation="vertical"
    >
      <div class="py-1" role="none">
        <div class="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide border-b border-gray-100">
          {$_('region.available_regions')}
        </div>
        
        {#each availableRegions as region (region.id)}
          <button
            type="button"
            class="group flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150"
            class:bg-blue-50={selectedRegionId === region.id}
            class:text-blue-700={selectedRegionId === region.id}
            role="menuitem"
            on:click={() => selectRegion(region.id)}
          >
            <div class="flex items-center justify-between w-full">
              <div class="flex items-center space-x-3" class:space-x-reverse={rtl}>
                <!-- Region flag or icon could go here -->
                <div class="flex-shrink-0">
                  {#if region.currency_code}
                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-xs font-mono font-semibold text-gray-600">
                      {getCurrencySymbol(region.currency_code)}
                    </span>
                  {:else}
                    <div class="w-8 h-8 rounded-full bg-gray-200"></div>
                  {/if}
                </div>
                
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {getRegionDisplayName(region)}
                  </p>
                  {#if region.currency_code}
                    <p class="text-xs text-gray-500">
                      {region.currency_code.toUpperCase()}
                    </p>
                  {/if}
                </div>
              </div>
              
              {#if selectedRegionId === region.id}
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              {/if}
            </div>
          </button>
        {/each}
        
        {#if availableRegions.length === 0}
          <div class="px-4 py-3 text-sm text-gray-500 text-center">
            {$_('region.no_regions_available')}
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>

<style>
  .region-selector {
    /* Ensure dropdown appears above other elements */
    z-index: 50;
  }
</style>
