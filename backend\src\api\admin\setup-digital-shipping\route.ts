import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"
import { Modules } from "@medusajs/framework/utils"

export const POST = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const fulfillmentModuleService = req.scope.resolve(Modules.FULFILLMENT)

  try {
    console.log('🔧 Setting up digital products shipping profile...')

    // Create a shipping profile for digital products
    const shippingProfile = await fulfillmentModuleService.createShippingProfiles({
      name: "Digital Products",
      type: "digital",
    })

    console.log('✅ Digital products shipping profile created:', shippingProfile.id)

    // Create a shipping option for digital delivery
    const shippingOption = await fulfillmentModuleService.createShippingOptions({
      name: "Digital Delivery",
      price_type: "flat",
      service_zone_id: "", // Digital products don't need zones
      shipping_profile_id: shippingProfile.id,
      provider_id: "digital-fulfillment",
      data: {
        type: "digital",
      },
      type: {
        label: "Digital Delivery",
        description: "Instant digital product delivery",
        code: "digital-delivery",
      },
    })

    console.log('✅ Digital delivery shipping option created:', shippingOption.id)

    res.json({
      success: true,
      shipping_profile: shippingProfile,
      shipping_option: shippingOption,
      message: "Digital products shipping setup completed successfully",
    })
  } catch (error) {
    console.error('❌ Failed to setup digital shipping:', error)
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to setup digital products shipping",
    })
  }
}
