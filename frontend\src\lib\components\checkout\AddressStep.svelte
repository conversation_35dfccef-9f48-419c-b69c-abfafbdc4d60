<script lang="ts">
  import { _ } from '$lib/i18n'

  export let shipping_address: any = {}
  export let billing_address: any = {}
  export let same_as_shipping: boolean = true
  export let isLoading: boolean = false
  export let onSubmit: (addresses: any) => void
  export let onBack: () => void

  let shippingForm = { ...shipping_address }
  let billingForm = { ...billing_address }
  let sameAsShipping = same_as_shipping
  let errors: Record<string, string> = {}

  // Update billing address when shipping changes and same_as_shipping is true
  $: if (sameAsShipping) {
    billingForm = { ...shippingForm }
  }

  function validateForm(): boolean {
    errors = {}
    
    // Validate shipping address
    if (!shippingForm.first_name?.trim()) {
      errors.shipping_first_name = $_('checkout.first_name_required')
    }
    if (!shippingForm.last_name?.trim()) {
      errors.shipping_last_name = $_('checkout.last_name_required')
    }
    if (!shippingForm.address_1?.trim()) {
      errors.shipping_address_1 = $_('checkout.address_required')
    }
    if (!shippingForm.city?.trim()) {
      errors.shipping_city = $_('checkout.city_required')
    }
    if (!shippingForm.postal_code?.trim()) {
      errors.shipping_postal_code = $_('checkout.postal_code_required')
    }

    // Validate billing address if different from shipping
    if (!sameAsShipping) {
      if (!billingForm.first_name?.trim()) {
        errors.billing_first_name = $_('checkout.first_name_required')
      }
      if (!billingForm.last_name?.trim()) {
        errors.billing_last_name = $_('checkout.last_name_required')
      }
      if (!billingForm.address_1?.trim()) {
        errors.billing_address_1 = $_('checkout.address_required')
      }
      if (!billingForm.city?.trim()) {
        errors.billing_city = $_('checkout.city_required')
      }
      if (!billingForm.postal_code?.trim()) {
        errors.billing_postal_code = $_('checkout.postal_code_required')
      }
    }

    return Object.keys(errors).length === 0
  }

  function handleSubmit() {
    if (!validateForm()) {
      return
    }

    const addresses = {
      shipping_address: shippingForm,
      billing_address: sameAsShipping ? shippingForm : billingForm
    }

    onSubmit(addresses)
  }
</script>

<div class="card-classic p-6">
  <div class="mb-6">
    <h2 class="font-book-title text-xl font-bold mb-2" style="color: var(--color-text-primary);">
      📍 {$_('checkout.step_address')}
    </h2>
    <p class="font-book-text" style="color: var(--color-text-secondary);">
      {$_('checkout.address_description')}
    </p>
  </div>

  <form on:submit|preventDefault={handleSubmit} class="space-y-6">
    <!-- Shipping Address -->
    <div>
      <h3 class="font-book-title text-lg font-semibold mb-4" style="color: var(--color-text-primary);">
        {$_('checkout.shipping_address')}
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="shipping_first_name" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.first_name')} *
          </label>
          <input
            id="shipping_first_name"
            type="text"
            bind:value={shippingForm.first_name}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.shipping_first_name}
            placeholder={$_('checkout.first_name_placeholder')}
            required
          />
          {#if errors.shipping_first_name}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.shipping_first_name}</p>
          {/if}
        </div>

        <div>
          <label for="shipping_last_name" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.last_name')} *
          </label>
          <input
            id="shipping_last_name"
            type="text"
            bind:value={shippingForm.last_name}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.shipping_last_name}
            placeholder={$_('checkout.last_name_placeholder')}
            required
          />
          {#if errors.shipping_last_name}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.shipping_last_name}</p>
          {/if}
        </div>
      </div>

      <div class="mt-4">
        <label for="shipping_address_1" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.address_line_1')} *
        </label>
        <input
          id="shipping_address_1"
          type="text"
          bind:value={shippingForm.address_1}
          disabled={isLoading}
          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          class:border-red-500={errors.shipping_address_1}
          placeholder={$_('checkout.address_line_1_placeholder')}
          required
        />
        {#if errors.shipping_address_1}
          <p class="mt-1 text-sm text-red-600 font-book-text">{errors.shipping_address_1}</p>
        {/if}
      </div>

      <div class="mt-4">
        <label for="shipping_address_2" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.address_line_2')}
        </label>
        <input
          id="shipping_address_2"
          type="text"
          bind:value={shippingForm.address_2}
          disabled={isLoading}
          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          placeholder={$_('checkout.address_line_2_placeholder')}
        />
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        <div>
          <label for="shipping_city" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.city')} *
          </label>
          <input
            id="shipping_city"
            type="text"
            bind:value={shippingForm.city}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.shipping_city}
            placeholder={$_('checkout.city_placeholder')}
            required
          />
          {#if errors.shipping_city}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.shipping_city}</p>
          {/if}
        </div>

        <div>
          <label for="shipping_postal_code" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.postal_code')} *
          </label>
          <input
            id="shipping_postal_code"
            type="text"
            bind:value={shippingForm.postal_code}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.shipping_postal_code}
            placeholder={$_('checkout.postal_code_placeholder')}
            required
          />
          {#if errors.shipping_postal_code}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.shipping_postal_code}</p>
          {/if}
        </div>

        <div>
          <label for="shipping_country" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.country')}
          </label>
          <select
            id="shipping_country"
            bind:value={shippingForm.country_code}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          >
            <option value="il">Israel</option>
            <option value="us">United States</option>
            <option value="gb">United Kingdom</option>
            <option value="ca">Canada</option>
          </select>
        </div>
      </div>

      <div class="mt-4">
        <label for="shipping_phone" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.phone')}
        </label>
        <input
          id="shipping_phone"
          type="tel"
          bind:value={shippingForm.phone}
          disabled={isLoading}
          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          placeholder={$_('checkout.phone_placeholder')}
        />
      </div>
    </div>

    <!-- Same as Shipping Checkbox -->
    <div class="flex items-center">
      <input
        id="same_as_shipping"
        type="checkbox"
        bind:checked={sameAsShipping}
        disabled={isLoading}
        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
      />
      <label for="same_as_shipping" class="ml-2 text-sm font-book-text" style="color: var(--color-text-primary);">
        {$_('checkout.billing_same_as_shipping')}
      </label>
    </div>

    <!-- Billing Address (if different) -->
    {#if !sameAsShipping}
      <div>
        <h3 class="font-book-title text-lg font-semibold mb-4" style="color: var(--color-text-primary);">
          {$_('checkout.billing_address')}
        </h3>
        
        <!-- Similar form fields for billing address -->
        <!-- (Implementation similar to shipping address above) -->
        <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
          {$_('checkout.billing_form_placeholder')}
        </p>
      </div>
    {/if}

    <!-- Form Actions -->
    <div class="flex justify-between pt-6">
      <button
        type="button"
        on:click={onBack}
        disabled={isLoading}
        class="btn-classic-outline"
        class:opacity-50={isLoading}
      >
        ← {$_('checkout.back')}
      </button>

      <button
        type="submit"
        disabled={isLoading}
        class="btn-classic"
        class:opacity-50={isLoading}
      >
        {#if isLoading}
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {$_('checkout.processing')}
          </div>
        {:else}
          {$_('checkout.continue')} →
        {/if}
      </button>
    </div>
  </form>
</div>
