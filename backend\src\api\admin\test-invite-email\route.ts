import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"
import {
  ContainerRegistrationKeys,
} from "@medusajs/framework/utils"
import { z } from "zod"

const testEmailSchema = z.object({
  email: z.string().email(),
  token: z.string(),
})

type TestEmailRequest = z.infer<typeof testEmailSchema>

// Test endpoint to manually send invite emails
export const POST = async (
  req: AuthenticatedMedusaRequest<TestEmailRequest>,
  res: MedusaResponse
) => {
  const { email, token } = req.validatedBody
  const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
  const notificationService = req.scope.resolve("notificationService")

  try {
    logger.info(`🧪 TEST: Sending admin invite email to: ${email}`)

    const emailResult = await notificationService.sendAdminInvite(email, token)

    if (emailResult.success) {
      logger.info(`✅ TEST: Admin invite email sent successfully to: ${email}`)

      res.json({
        success: true,
        message: "Test invite email sent successfully",
        invite_url: `${process.env.ADMIN_URL || 'http://localhost:9000'}/app/invite?token=${token}`,
      })
    } else {
      logger.error(`❌ TEST: Failed to send admin invite email to: ${email}`, emailResult.error)
      res.status(500).json({
        success: false,
        message: "Failed to send test invite email",
        error: emailResult.error,
      })
    }
  } catch (error) {
    logger.error(`❌ TEST: Error sending admin invite email:`, error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    })
  }
}
