import { 
  createWorkflow, 
  WorkflowResponse,
} from "@medusajs/framework/workflows-sdk"
import { deleteDigitalProductStep } from "./steps/delete-digital-product"

type DeleteDigitalProductWorkflowInput = {
  variant_id: string
}

export const deleteDigitalProductWorkflow = createWorkflow(
  "delete-digital-product",
  (input: DeleteDigitalProductWorkflowInput) => {
    deleteDigitalProductStep(input)

    return new WorkflowResponse(input)
  }
)

export default deleteDigitalProductWorkflow
