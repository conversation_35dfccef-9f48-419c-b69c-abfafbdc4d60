import {
  SubscriberArgs,
  SubscriberConfig,
} from "@medusajs/framework"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

async function orderPlacedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  console.log('🔔 ORDER PLACED EVENT RECEIVED')
  console.log('📋 Event Data:', JSON.stringify(data, null, 2))
  console.log('⏰ Timestamp:', new Date().toISOString())

  const query = container.resolve(ContainerRegistrationKeys.QUERY)

  console.log('🔍 Fetching order details for ID:', data.id)

  // Get the order with its items and linked digital products
  const { data: [order] } = await query.graph({
    entity: "order",
    fields: [
      "id",
      "email",
      "status",
      "total",
      "currency_code",
      "customer_id",
      "items.*",
      "items.variant.*",
      "items.variant.product.*",
      "items.variant.digital_product.*",
      "items.variant.digital_product.medias.*",
      "fulfillments.*",
    ],
    filters: {
      id: data.id,
    },
  })

  console.log('📦 ORDER DETAILS RETRIEVED:')
  console.log('  - Order ID:', order?.id)
  console.log('  - Customer Email:', order?.email)
  console.log('  - Customer ID:', order?.customer_id)
  console.log('  - Order Status:', order?.status)
  console.log('  - Total Amount:', order?.total, order?.currency_code)
  console.log('  - Items Count:', order?.items?.length || 0)
  console.log('  - Fulfillments Count:', order?.fulfillments?.length || 0)

  if (!order || !order.items) {
    console.log('❌ No order or items found, exiting handler')
    return
  }

  console.log('📋 ANALYZING ORDER ITEMS:')
  order.items.forEach((item: any, index: number) => {
    console.log(`  Item ${index + 1}:`)
    console.log('    - Product ID:', item.variant?.product?.id)
    console.log('    - Product Title:', item.variant?.product?.title)
    console.log('    - Variant ID:', item.variant?.id)
    console.log('    - Variant Title:', item.variant?.title)
    console.log('    - Quantity:', item.quantity)
    console.log('    - Unit Price:', item.unit_price)
    console.log('    - Has Digital Product Link:', !!item.variant?.digital_product)
    if (item.variant?.digital_product) {
      console.log('    - Digital Product ID:', item.variant.digital_product.id)
      console.log('    - Digital Product Name:', item.variant.digital_product.name)
      console.log('    - Digital Product Medias:', item.variant.digital_product.medias?.length || 0)
    }
  })

  // Find all digital products in the order
  const digitalProductIds: string[] = []

  order.items.forEach((item: any) => {
    if (item.variant?.digital_product) {
      digitalProductIds.push(item.variant.digital_product.id)
    }
  })

  console.log('🔍 DIGITAL PRODUCTS ANALYSIS:')
  console.log('  - Digital Products Found:', digitalProductIds.length)
  console.log('  - Digital Product IDs:', digitalProductIds)

  // If there are digital products, create a digital product order
  if (digitalProductIds.length > 0) {
    console.log('🚀 DIGITAL PRODUCTS FOUND - PROCESSING FULFILLMENT')
    console.log('  - Order ID:', order.id)
    console.log('  - Digital Product IDs:', digitalProductIds)
    console.log('  - Note: Digital fulfillment will be handled by digital-order-fulfillment subscriber')
    console.log('  - This subscriber focuses on order processing, not digital fulfillment')
  } else {
    console.log('ℹ️ No digital products in order, skipping digital fulfillment')
  }

  console.log('✅ ORDER PLACED HANDLER COMPLETED')
  console.log('=' .repeat(80))
}

export default orderPlacedHandler

export const config: SubscriberConfig = {
  event: "order.placed",
}
