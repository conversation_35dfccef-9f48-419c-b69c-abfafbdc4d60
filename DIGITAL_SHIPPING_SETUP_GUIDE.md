# 🚢 Digital Shipping Options Setup Guide

## 🎯 **Overview**
Digital products need special shipping options because they are delivered instantly via download links, not physical shipping.

## 📋 **Prerequisites**
- ✅ Digital fulfillment provider configured (`digital-fulfillment`)
- ✅ Digital products created and linked to variants
- ✅ MedusaJS backend running

---

## 🛠️ **Method 1: Admin Panel Setup (Corrected)**

### **Step 1: Create Digital Shipping Profile**
1. **Navigate**: Admin Panel → Settings → **Locations and Shipping**
2. **Find**: "Shipping Profiles" section
3. **Click**: "Create Shipping Profile" or "+" button
4. **Configure**:
   ```
   Name: Digital Products
   Type: default
   ```
5. **Save** the profile

### **Step 2: Link Products to Shipping Profile**
**Note**: In MedusaJS v2, products are linked to shipping profiles through the **Products** section, not the profile itself.

1. **Navigate**: Products → [Select a Digital Product]
2. **Go to**: Product details/edit page
3. **Find**: "Shipping" or "Shipping Profile" section
4. **Select**: "Digital Products" from the dropdown
5. **Save** the product
6. **Repeat** for all digital products

### **Step 3: Create Service Zone (if needed)**
1. **Navigate**: Settings → Locations and Shipping → **Locations**
2. **Select**: Your stock location
3. **Go to**: "Fulfillment" or "Service Zones" tab
4. **Create/Edit**: Service Zone
5. **Configure**:
   ```
   Name: Global Digital Delivery
   Countries: Add countries (or select "All countries")
   ```

### **Step 4: Create Digital Shipping Option**
1. **Navigate**: Settings → Locations and Shipping → **Shipping Options**
2. **Click**: "Create Shipping Option" or "+" button
3. **Configure**:
   ```
   Name: Digital Delivery
   Provider: digital-fulfillment
   Shipping Profile: Digital Products
   Service Zone: Global Digital Delivery
   Price Type: Flat Rate
   Amount: 0
   Currency: USD (or your currency)
   ```
4. **Save**

### **🔍 Detailed Admin Panel Navigation**

#### **Finding Shipping Profiles**:
```
Admin Panel → Settings (gear icon) → Locations and Shipping →
Look for "Shipping Profiles" section (may be in a tab or accordion)
```

#### **Finding Shipping Options**:
```
Admin Panel → Settings → Locations and Shipping →
Look for "Shipping Options" section
```

#### **Linking Products to Profiles**:
```
Admin Panel → Products → [Click on Digital Product] →
Edit button → Scroll to find "Shipping" or "Fulfillment" section →
Dropdown for "Shipping Profile"
```

**Note**: If you can't find these sections, they might be:
- In different tabs within the page
- Under different names (e.g., "Fulfillment" instead of "Shipping")
- Require specific permissions
- Hidden in collapsed sections

---

## 🤖 **Method 2: Automated Script Setup (Recommended)**

### **Run the Setup Script**:
```bash
cd backend
npx medusa exec ./src/scripts/setup-digital-shipping.ts
```

### **What the Script Does**:
1. ✅ Creates "Digital Products" shipping profile
2. ✅ Creates digital shipping option with `digital-fulfillment` provider
3. ✅ Sets up free pricing (0 cost)
4. ✅ Configures proper service zones
5. ✅ Links everything together

---

## 🔧 **Method 3: Manual API Setup**

### **Create Shipping Profile**:
```typescript
const shippingProfile = await fulfillmentModuleService.createShippingProfiles({
  name: "Digital Products",
  type: "Digital",
})
```

### **Create Shipping Option**:
```typescript
const digitalShippingOption = await fulfillmentModuleService.createShippingOptions({
  name: "Digital Product Delivery",
  price_type: "flat",
  service_zone_id: serviceZone.id, // Your service zone
  shipping_profile_id: shippingProfile.id,
  provider_id: "digital-fulfillment",
  type_id: shippingOptionType.id,
  data: {
    delivery_method: "digital",
    instant_delivery: true,
  },
  prices: [
    {
      currency_code: "USD",
      amount: 0, // Free digital delivery
    },
  ],
})
```

---

## 🔍 **Verification Steps**

### **1. Check Shipping Options**:
```bash
npx medusa exec ./src/scripts/check-shipping-options.ts
```

### **2. Expected Output**:
```
🎯 DIGITAL FULFILLMENT OPTIONS: 1
1. Digital Product Delivery
   - Has Service Zone: YES
   - Has Shipping Profile: YES
```

### **3. Test Order Flow**:
1. Add digital product to cart
2. Go to checkout
3. **Verify**: "Digital Delivery" appears as shipping option
4. **Verify**: Price is 0 (free)
5. Complete order
6. **Verify**: Order gets fulfilled automatically

---

## 🚨 **Common Issues & Solutions**

### **Issue 0: Can't Find Admin Sections**
**Problem**: Admin interface differs from documentation
**Real Locations**:
- ❌ ~~Settings → Shipping Profiles~~
- ✅ **Settings → Locations and Shipping → Shipping Profiles**
- ❌ ~~Settings → Shipping Options~~
- ✅ **Settings → Locations and Shipping → Shipping Options**
- ❌ ~~Product → Shipping Profile~~
- ✅ **Products → [Product] → Edit → Shipping section**

### **Issue 1: No Shipping Options Appear**
**Cause**: Shipping profile not linked to digital products
**Solution**:
1. Go to Products → [Digital Product] → Edit
2. Find "Shipping" section in product details
3. Select "Digital Products" from shipping profile dropdown
4. Save the product

### **Issue 2: "service_zone undefined" Error**
**Cause**: Shipping option not linked to service zone
**Solution**:
1. Create service zone first
2. Link shipping option to service zone

### **Issue 3: Orders Stay "Pending"**
**Cause**: No fulfillment created
**Solution**:
1. Check if digital shipping option was selected during checkout
2. Verify `digital-order-fulfillment.ts` subscriber is working
3. Check server logs for errors

### **Issue 4: Multiple Fulfillment Providers**
**Cause**: Conflicting providers (manual vs digital)
**Solution**:
1. Use only `digital-fulfillment` for digital products
2. Use `manual` for physical products

---

## 📊 **Database Structure**

### **Key Tables**:
```sql
-- Shipping profiles for digital products
shipping_profile (name: "Digital Products")

-- Shipping options using digital provider
shipping_option (provider_id: "digital-fulfillment")

-- Service zones for delivery areas
service_zone (name: "Global Digital Delivery")

-- Pricing (free for digital)
shipping_option_price (amount: 0)
```

---

## 🎯 **Best Practices**

### **1. Naming Convention**:
- **Profile**: "Digital Products"
- **Option**: "Digital Delivery" or "Instant Download"
- **Zone**: "Global Digital Delivery"

### **2. Pricing**:
- Always set digital delivery to **0 cost**
- Support multiple currencies if needed

### **3. Service Zones**:
- Create **global zone** for worldwide digital delivery
- No geographic restrictions for digital products

### **4. Testing**:
- Test complete order flow after setup
- Verify automatic fulfillment works
- Check email notifications are sent

---

## 🔄 **Integration with Your System**

### **Your Current Setup**:
```typescript
// Provider: digital-fulfillment (✅ Already configured)
// Subscriber: digital-order-fulfillment.ts (✅ Already created)
// Workflow: fulfill-digital-order (✅ Already implemented)
```

### **Missing Piece**:
```typescript
// Shipping Options: ❌ Need to be created
// This is why orders stay "pending"
```

---

## 🚀 **Quick Start Commands**

### **1. Setup Everything**:
```bash
cd backend
npx medusa exec ./src/scripts/setup-digital-shipping.ts
```

### **2. Verify Setup**:
```bash
npx medusa exec ./src/scripts/check-shipping-options.ts
```

### **3. Test with Existing Order**:
```bash
npx medusa exec ./src/scripts/manual-fulfill-order-18.ts
```

---

## ✅ **Success Indicators**

After setup, you should see:
- ✅ Digital shipping option in admin panel
- ✅ "Digital Delivery" appears in checkout
- ✅ Orders with digital products get fulfilled automatically
- ✅ Customers receive download links via email
- ✅ Order status changes from "pending" to "fulfilled"

---

## 📞 **Need Help?**

If you encounter issues:
1. Check server logs: `tail -f logs/medusa.log`
2. Verify provider registration in `medusa-config.ts`
3. Test with a new order (not existing ones)
4. Check that digital products are linked to the correct shipping profile
