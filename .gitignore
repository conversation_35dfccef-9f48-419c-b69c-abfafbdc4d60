# Dependencies
node_modules/
/node_modules
frontend/node_modules/
backend/node_modules/

# Environment variables (IMPORTANT: Contains API keys and secrets)
.env
.env.local
.env.production
.env.development
frontend/.env.local
backend/.env
!.env.example

# Build outputs
/dist
/build
frontend/build/
backend/dist/
.output
.vercel
.netlify
.wrangler

# SvelteKit
frontend/.svelte-kit/
.svelte-kit/

# Medusa.js
.medusa/
backend/.medusa/
medusa-db.sql

# Logs
*.log
yarn-error.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Cache
.cache/
.parcel-cache/
coverage/

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# Uploads
/uploads