# 📧 Mailjet Email Configuration

## 🚀 Quick Setup

### Step 1: Get Mailjet API Keys

1. **Sign up** at [Mailjet](https://app.mailjet.com/signup)
2. **Verify your account** via email
3. **Go to API Keys**: [https://app.mailjet.com/account/apikeys](https://app.mailjet.com/account/apikeys)
4. **Copy your credentials**:
   - API Key (Public Key)
   - Secret Key (Private Key)

### Step 2: Configure Environment Variables

Update your `backend/.env` file:

```env
# Email Service (Mailjet)
MAILJET_API_KEY=your_actual_api_key_here
MAILJET_SECRET_KEY=your_actual_secret_key_here
FROM_EMAIL=<EMAIL>
```

### Step 3: Verify Sender Email

1. **Go to Sender Settings**: [https://app.mailjet.com/account/sender](https://app.mailjet.com/account/sender)
2. **Add your domain** or **verify individual email**
3. **Follow verification steps** (DNS records or email confirmation)

## 📋 Email Templates Available

The system now supports these email templates:

### Customer Emails
- ✅ **Purchase Confirmation** - Sent after successful order
- ✅ **Welcome Email** - Sent to new customers
- ✅ **Subscription Confirmation** - Sent when subscription is activated
- ✅ **Subscription Expiration** - Sent before subscription expires
- ✅ **Password Reset** - Sent for password reset requests

### Admin Emails
- ✅ **Admin Invite** - Sent when creating new admin users

## 🧪 Testing Email Functionality

### Test Admin Invite Email

1. **Create an admin invite**:
   ```bash
   cd backend
   medusa user --email <EMAIL> --invite
   ```

2. **Check your email** - You should receive an invitation email

3. **Click the invite link** to complete setup

### Test Customer Emails

You can test customer emails by:
- Creating a test customer account
- Making a test purchase
- Requesting password reset

## 🔧 Troubleshooting

### Common Issues

**1. "Authentication failed" error**
- ✅ Check API keys are correct
- ✅ Ensure no extra spaces in environment variables
- ✅ Verify account is activated

**2. "Sender not verified" error**
- ✅ Verify your sender email/domain in Mailjet dashboard
- ✅ Check DNS records if using domain verification

**3. Emails not being sent**
- ✅ Check backend logs for error messages
- ✅ Verify Mailjet account has sending quota
- ✅ Check spam folder

### Debug Mode

Add this to your backend logs to see email sending details:

```typescript
// In notification service
console.log('Sending email to:', template.to)
console.log('Email result:', result)
```

## 🌍 Production Considerations

### 1. Domain Verification
- Use domain verification instead of individual email verification
- Set up SPF, DKIM, and DMARC records

### 2. Sending Limits
- Free plan: 200 emails/day, 6,000 emails/month
- Check your usage in Mailjet dashboard

### 3. Email Templates
- Consider using Mailjet's template system for better deliverability
- Test emails across different email clients

### 4. Monitoring
- Monitor bounce rates and spam complaints
- Set up webhooks for delivery tracking

## 📚 Resources

- [Mailjet Documentation](https://dev.mailjet.com/)
- [Node.js SDK](https://github.com/mailjet/mailjet-apiv3-nodejs)
- [Email Best Practices](https://dev.mailjet.com/email/guides/)
- [Deliverability Guide](https://dev.mailjet.com/email/guides/deliverability/)

## 🎯 Next Steps

After setting up Mailjet:

1. **Test all email templates** with real email addresses
2. **Set up domain verification** for production
3. **Configure email monitoring** and analytics
4. **Review email content** for your brand voice
5. **Set up email automation** workflows if needed

---

**Note**: Replace all placeholder values with your actual Mailjet credentials and domain information.
