#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create admin invites with automatic email sending
 * Usage: node scripts/create-admin-invite.js <email>
 * Example: node scripts/create-admin-invite.js <EMAIL>
 */

const { execSync } = require('child_process')
const jwt = require('jsonwebtoken')
require('dotenv').config()

async function createAdminInviteWithEmail(email) {
  if (!email) {
    console.error('❌ Email is required')
    console.log('Usage: node scripts/create-admin-invite.js <email>')
    process.exit(1)
  }

  try {
    console.log(`🔄 Creating admin invite for: ${email}`)
    
    // Step 1: Create invite using Medusa CLI
    console.log('📝 Creating invite via Medusa CLI...')
    const cliOutput = execSync(`medusa user --email ${email} --invite`, { 
      encoding: 'utf8',
      cwd: process.cwd()
    })
    
    console.log('CLI Output:', cliOutput)
    
    // Step 2: Extract token from CLI output
    const tokenMatch = cliOutput.match(/Invite token: ([^\s]+)/)
    if (!tokenMatch) {
      console.error('❌ Could not extract invite token from CLI output')
      console.log('CLI Output:', cliOutput)
      process.exit(1)
    }
    
    const inviteToken = tokenMatch[1]
    console.log(`✅ Invite token extracted: ${inviteToken.substring(0, 20)}...`)
    
    // Step 3: Send email using our notification service
    console.log('📧 Sending invite email...')
    
    // Import and use the notification service
    const { default: NotificationService } = await import('../src/modules/notification/service.ts')
    const notificationService = new NotificationService()
    
    const emailResult = await notificationService.sendAdminInvite(email, inviteToken)
    
    if (emailResult.success) {
      console.log(`✅ Admin invite email sent successfully to: ${email}`)
      
      // Step 4: Display invite URL
      const inviteUrl = `${process.env.ADMIN_URL || 'http://localhost:9000'}/app/invite?token=${inviteToken}`
      console.log(`🔗 Invite URL: ${inviteUrl}`)
      
      console.log('\n🎉 Admin invite created successfully!')
      console.log(`📧 Email sent to: ${email}`)
      console.log(`🔗 Invite link: ${inviteUrl}`)
      console.log(`⏰ Token expires in 7 days`)
      
    } else {
      console.error(`❌ Failed to send invite email:`, emailResult.error)
      
      // Still show the invite URL even if email failed
      const inviteUrl = `${process.env.ADMIN_URL || 'http://localhost:9000'}/app/invite?token=${inviteToken}`
      console.log(`🔗 Manual invite URL: ${inviteUrl}`)
      
      process.exit(1)
    }
    
  } catch (error) {
    console.error('❌ Error creating admin invite:', error.message)
    process.exit(1)
  }
}

// Get email from command line arguments
const email = process.argv[2]
createAdminInviteWithEmail(email)
