import {
  createStep,
  StepResponse,
} from "@medusajs/framework/workflows-sdk"
import {
  INotificationModuleService,
} from "@medusajs/framework/types"
import {
  ModuleRegistrationName,
} from "@medusajs/framework/utils"
import { DigitalProductOrder, MediaType } from "../../../modules/digital-product/types"

type SendDigitalOrderNotificationStepInput = {
  digital_product_order: DigitalProductOrder | any
}

export const sendDigitalOrderNotificationStep = createStep(
  "send-digital-order-notification",
  async ({ 
    digital_product_order: digitalProductOrder, 
  }: SendDigitalOrderNotificationStepInput, 
  { container }) => {
    const notificationModuleService: INotificationModuleService = container
    .resolve(ModuleRegistrationName.NOTIFICATION)

    console.log('📧 Sending digital product notification for order:', digitalProductOrder.order?.id)

    // Safely handle products array
    const products = digitalProductOrder.products || []
    console.log('📧 Products to notify about:', products.length)

    // Assemble notification data
    const notificationData = products.map((product) => {
      const mainMedias = product.medias?.filter((media) => media.type === MediaType.MAIN) || []

      return {
        name: product.name,
        medias: mainMedias.map(media => ({
          id: media.id,
          url: media.url || `Download link for ${product.name}`,
          mimeType: media.mimeType,
        })),
      }
    })

    // Skip notification if no products
    if (products.length === 0) {
      console.log('⚠️ No products found for notification, skipping')
      return new StepResponse({
        notification_id: 'skipped',
        order_id: digitalProductOrder.order?.id,
        message: 'No products to notify about',
      })
    }

    // Send notification with error handling
    try {
      const notification = await notificationModuleService.createNotifications({
        to: digitalProductOrder.order?.email || '',
        template: "digital-order-template",
        channel: "email",
        data: {
          order_id: digitalProductOrder.order?.id,
          customer_name: `${digitalProductOrder.order?.billing_address?.first_name || ''} ${digitalProductOrder.order?.billing_address?.last_name || ''}`.trim(),
          products: notificationData,
        },
      })

      console.log('✅ Digital product notification sent:', notification.id)

      return new StepResponse({
        notification_id: notification.id,
        order_id: digitalProductOrder.order?.id,
      })
    } catch (error) {
      console.error('❌ Error sending digital product notification:', error)

      // Return success even if notification fails to prevent workflow failure
      return new StepResponse({
        notification_id: 'failed',
        order_id: digitalProductOrder.order?.id,
        error: error.message,
      })
    }


  }
)

export default sendDigitalOrderNotificationStep
