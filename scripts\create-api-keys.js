#!/usr/bin/env node

// Hebrew Book Store - API Keys Creation Script
// This script creates API keys and test data via Medusa.js API

const fetch = require('node-fetch');

const MEDUSA_URL = 'http://localhost:9000';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkMedusaHealth() {
  try {
    const response = await fetch(`${MEDUSA_URL}/health`);
    if (response.ok) {
      log('✅ Medusa.js server is running', 'green');
      return true;
    }
  } catch (error) {
    log('❌ Medusa.js server is not running', 'red');
    log('Please start the server with: cd backend && yarn dev', 'yellow');
    return false;
  }
}

async function createPublishableKey() {
  try {
    log('Creating publishable API key...', 'blue');
    
    // Try to create via admin API (might need authentication)
    const response = await fetch(`${MEDUSA_URL}/admin/publishable-api-keys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: 'Hebrew Book Store Development Key'
      })
    });

    if (response.ok) {
      const data = await response.json();
      log(`✅ Publishable key created: ${data.publishable_api_key.id}`, 'green');
      return data.publishable_api_key;
    } else {
      const error = await response.text();
      log(`⚠️ Could not create via API: ${error}`, 'yellow');
      return null;
    }
  } catch (error) {
    log(`⚠️ API creation failed: ${error.message}`, 'yellow');
    return null;
  }
}

async function createTestUser() {
  try {
    log('Creating test user...', 'blue');
    
    const response = await fetch(`${MEDUSA_URL}/admin/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User'
      })
    });

    if (response.ok) {
      const data = await response.json();
      log(`✅ User created: ${data.user.email}`, 'green');
      return data.user;
    } else {
      const error = await response.text();
      log(`⚠️ Could not create user via API: ${error}`, 'yellow');
      return null;
    }
  } catch (error) {
    log(`⚠️ User creation failed: ${error.message}`, 'yellow');
    return null;
  }
}

async function testStoreAPI() {
  try {
    log('Testing store API...', 'blue');
    
    // Test without API key first
    const response = await fetch(`${MEDUSA_URL}/store/products`);
    
    if (response.ok) {
      const data = await response.json();
      log(`✅ Store API working (${data.products?.length || 0} products)`, 'green');
      return true;
    } else {
      log(`⚠️ Store API requires publishable key`, 'yellow');
      return false;
    }
  } catch (error) {
    log(`⚠️ Store API test failed: ${error.message}`, 'yellow');
    return false;
  }
}

async function updateFrontendEnv(publishableKey) {
  const fs = require('fs');
  const path = require('path');
  
  try {
    const envPath = path.join(__dirname, '../frontend/.env');
    
    if (fs.existsSync(envPath)) {
      let content = fs.readFileSync(envPath, 'utf8');
      
      if (publishableKey) {
        content = content.replace(
          /VITE_MEDUSA_PUBLISHABLE_API_KEY=.*/,
          `VITE_MEDUSA_PUBLISHABLE_API_KEY=${publishableKey}`
        );
      }
      
      fs.writeFileSync(envPath, content);
      log('✅ Frontend .env updated', 'green');
    } else {
      log('⚠️ Frontend .env not found', 'yellow');
    }
  } catch (error) {
    log(`⚠️ Failed to update .env: ${error.message}`, 'yellow');
  }
}

async function main() {
  log('🚀 Hebrew Book Store - API Setup', 'cyan');
  log('================================', 'cyan');
  
  // Check if Medusa is running
  const medusaRunning = await checkMedusaHealth();
  if (!medusaRunning) {
    process.exit(1);
  }
  
  // Try to create API keys and users
  const publishableKey = await createPublishableKey();
  const user = await createTestUser();
  
  // Test store API
  await testStoreAPI();
  
  // Update frontend environment
  if (publishableKey) {
    await updateFrontendEnv(publishableKey.id);
  }
  
  // Display summary
  log('\n🎉 Setup Summary:', 'cyan');
  log('================', 'cyan');
  
  if (publishableKey) {
    log(`✅ Publishable Key: ${publishableKey.id}`, 'green');
  } else {
    log('⚠️ Publishable Key: Not created (use fallback mode)', 'yellow');
  }
  
  if (user) {
    log(`✅ Test User: ${user.email}`, 'green');
  } else {
    log('⚠️ Test User: Not created (use fallback mode)', 'yellow');
  }
  
  log('\n🔗 URLs:', 'cyan');
  log(`  • Frontend: http://localhost:5173`, 'blue');
  log(`  • Admin: http://localhost:5173/admin/login`, 'blue');
  log(`  • API: ${MEDUSA_URL}`, 'blue');
  
  log('\n👤 Test Credentials:', 'cyan');
  log(`  • Email: <EMAIL>`, 'blue');
  log(`  • Password: admin123`, 'blue');
  
  if (!publishableKey || !user) {
    log('\n📦 Fallback Mode:', 'yellow');
    log('  The system will work in fallback mode with local data.', 'yellow');
    log('  All features are available for testing!', 'yellow');
  }
}

// Run the script
main().catch(error => {
  log(`❌ Script failed: ${error.message}`, 'red');
  process.exit(1);
});
