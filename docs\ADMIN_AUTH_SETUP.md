# Medusa.js v2 - Настройка Admin Authentication

## 🎯 Обзор

Medusa.js v2 использует **invite-based систему аутентификации** для админских пользователей. Это означает, что нельзя просто создать пользователя с паролем - нужно создать приглашение (invite), активировать его, и только потом пользователь может войти в систему.

## 🔄 Архитектура аутентификации

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CLI Command   │    │   Invite Token   │    │  Admin Panel    │
│                 │───►│                  │───►│                 │
│ medusa user     │    │  JWT with expiry │    │ Accept Invite   │
│ --invite        │    │                  │    │ Set Password    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌──────────────────┐
                    │   Auth Identity  │
                    │   + Provider     │
                    │   Identity       │
                    └──────────────────┘
                                │
                                ▼
                    ┌──────────────────┐
                    │   User Record    │
                    │   in Database    │
                    └──────────────────┘
```

## 📋 Пошаговая инструкция

### Шаг 1: Создание Invite

```bash
cd backend
medusa user --email <EMAIL> --invite
```

**Ожидаемый вывод**:
```
info: Invite token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
      Open the invite in Medusa Admin at: [your-admin-url]/invite?token=...
```

### Шаг 2: Активация Invite

1. **Скопируйте invite token** из вывода команды
2. **Откройте URL активации**:
   ```
   http://localhost:5173/admin/invite?token=[INVITE_TOKEN]
   ```
3. **На странице активации**:
   - Введите желаемый пароль (например: `admin123`)
   - Подтвердите пароль
   - Нажмите "Accept Invite"

### Шаг 3: Вход в систему

После успешной активации:
```
URL: http://localhost:5173/admin/login
Email: <EMAIL>
Password: admin123
```

## 🗄️ Что происходит в базе данных

### Таблицы, участвующие в аутентификации:

1. **`user`** - основная информация о пользователе
2. **`auth_identity`** - идентификация для аутентификации
3. **`provider_identity`** - связь с провайдером аутентификации (emailpass)
4. **`invite`** - приглашения пользователей

### Процесс создания invite:

```sql
-- 1. Создается запись в таблице invite
INSERT INTO invite (id, email, token, expires_at, accepted, created_at, updated_at)
VALUES ('invite_123', '<EMAIL>', 'jwt_token', '2024-01-01', false, NOW(), NOW());
```

### Процесс активации invite:

```sql
-- 1. Создается пользователь
INSERT INTO "user" (id, email, first_name, last_name, created_at, updated_at)
VALUES ('user_123', '<EMAIL>', 'Admin', 'User', NOW(), NOW());

-- 2. Создается auth_identity
INSERT INTO auth_identity (id, app_metadata, created_at, updated_at)
VALUES ('auth_123', '{}', NOW(), NOW());

-- 3. Создается provider_identity с паролем
INSERT INTO provider_identity (id, entity_id, provider, auth_identity_id, user_metadata, provider_metadata, created_at, updated_at)
VALUES ('provider_123', 'user_123', 'emailpass', 'auth_123', '{"email":"<EMAIL>"}', '{"password_hash":"..."}', NOW(), NOW());

-- 4. Invite помечается как принятый
UPDATE invite SET accepted = true WHERE id = 'invite_123';
```

## ❌ Частые ошибки

### 1. Попытка создать пользователя напрямую

**Неправильно**:
```bash
medusa user --email <EMAIL> --password admin123
```

**Почему не работает**: Создается только запись в таблице `user`, но не создаются записи в `auth_identity` и `provider_identity`, необходимые для аутентификации.

### 2. Ручное создание через SQL

**Неправильно**:
```sql
INSERT INTO "user" (email, password_hash) VALUES ('<EMAIL>', 'hash');
```

**Почему не работает**: В таблице `user` нет поля `password_hash`. Пароли хранятся в `provider_identity.provider_metadata`.

### 3. Использование старого токена

**Ошибка**: Invite токены имеют срок действия (обычно 24 часа).

**Решение**: Создайте новый invite, если старый истек.

## 🔧 Команды для отладки

### Проверка существующих пользователей:
```sql
SELECT id, email, first_name, last_name FROM "user";
```

### Проверка invite:
```sql
SELECT id, email, accepted, expires_at FROM invite WHERE email = '<EMAIL>';
```

### Проверка auth_identity:
```sql
SELECT ai.id, pi.entity_id, pi.provider, u.email 
FROM auth_identity ai
JOIN provider_identity pi ON ai.id = pi.auth_identity_id
JOIN "user" u ON pi.entity_id = u.id;
```

### Удаление пользователя для пересоздания:
```sql
-- Удалить все связанные записи
DELETE FROM provider_identity WHERE entity_id IN (SELECT id FROM "user" WHERE email = '<EMAIL>');
DELETE FROM auth_identity WHERE id NOT IN (SELECT auth_identity_id FROM provider_identity);
DELETE FROM invite WHERE email = '<EMAIL>';
DELETE FROM "user" WHERE email = '<EMAIL>';
```

## 🚀 Автоматизация

### Скрипт для быстрой настройки админа:

```bash
#!/bin/bash
# setup-admin.sh

EMAIL="<EMAIL>"
PASSWORD="admin123"

echo "🔄 Создание invite для $EMAIL..."
INVITE_OUTPUT=$(medusa user --email $EMAIL --invite)
TOKEN=$(echo "$INVITE_OUTPUT" | grep "Invite token:" | cut -d' ' -f3)

echo "✅ Invite создан!"
echo "🔗 Откройте: http://localhost:5173/admin/invite?token=$TOKEN"
echo "🔑 Установите пароль: $PASSWORD"
echo "📧 Затем войдите с: $EMAIL / $PASSWORD"
```

## 📚 Дополнительные ресурсы

- [Medusa.js Auth Module Documentation](https://docs.medusajs.com/resources/commerce-modules/auth)
- [User Creation Flows](https://docs.medusajs.com/resources/commerce-modules/user/user-creation-flows)
- [Authentication Routes](https://docs.medusajs.com/resources/commerce-modules/auth/authentication-route)

## 🎯 Заключение

Invite-based система в Medusa.js v2 обеспечивает:
- **Безопасность**: Контролируемое создание админских аккаунтов
- **Гибкость**: Поддержка различных провайдеров аутентификации
- **Масштабируемость**: Легкое управление пользователями в больших командах

**Главное правило**: Всегда используйте `medusa user --invite` для создания админов в Medusa.js v2!
