# Checkout & Payment Integration Guide

This guide explains how to set up and test the checkout process with Stripe payment integration in your Hebrew Book Store.

## Overview

The checkout system provides:
- **Authenticated user checkout** (no guest checkout)
- **Multi-step checkout process** (Email → Address → Shipping → Payment)
- **Stripe payment integration** with test environment
- **Order completion and confirmation**
- **Persistent cart management** across sessions

## Setup Instructions

### 1. Backend Configuration (Already Done)

The backend is already configured with Stripe payment provider:

```typescript
// backend/medusa-config.ts
modules: [
  {
    resolve: "@medusajs/medusa/payment",
    options: {
      providers: [
        {
          resolve: "@medusajs/medusa/payment-stripe",
          id: "stripe",
          options: {
            apiKey: process.env.STRIPE_API_KEY,
            webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
          },
        },
      ],
    },
  },
]
```

### 2. Environment Variables

#### Backend (.env):
```bash
# Stripe Configuration (Test Keys)
STRIPE_API_KEY=sk_test_...your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_...your_webhook_secret_here
```

#### Frontend (.env.local):
```bash
# Stripe Configuration (Test Keys)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...your_stripe_publishable_key_here
```

### 3. Get Stripe Test Keys

1. **Create Stripe Account**:
   - Go to https://stripe.com
   - Create a free account
   - Navigate to Dashboard

2. **Get Test API Keys**:
   - Go to Developers → API Keys
   - Copy "Publishable key" (starts with `pk_test_`)
   - Copy "Secret key" (starts with `sk_test_`)

3. **Update Environment Files**:
   - Add keys to backend `.env` and frontend `.env.local`
   - Restart both backend and frontend

## Checkout Flow

### Step 1: Email Verification
- Customer enters email address
- Email is validated and saved to cart
- Proceeds to address step

### Step 2: Shipping & Billing Address
- Customer enters shipping address
- Option to use same address for billing
- Address validation and cart update

### Step 3: Shipping Method Selection
- Available shipping options loaded from MedusaJS
- Customer selects preferred shipping method
- Shipping cost calculated and added to cart

### Step 4: Payment Processing
- Payment sessions initialized with Stripe
- Customer enters credit card details
- Secure payment processing via Stripe
- Order completion and confirmation

## Testing the Checkout

### Prerequisites
1. **User must be authenticated** (no guest checkout)
2. **Cart must have items** (add products from catalog)
3. **Stripe test keys configured** (see setup above)

### Test Flow
1. **Add Items to Cart**:
   - Go to `/catalog`
   - Add books/chapters to cart
   - Verify cart shows items

2. **Start Checkout**:
   - Click cart icon
   - Click "Proceed to Checkout" button
   - Should redirect to `/checkout`

3. **Complete Checkout Steps**:
   - **Email**: Enter valid email address
   - **Address**: Fill shipping/billing information
   - **Shipping**: Select shipping method
   - **Payment**: Enter test card details

### Stripe Test Cards

Use these test card numbers for testing:

#### Successful Payments:
- **Visa**: `****************`
- **Visa (debit)**: `****************`
- **Mastercard**: `****************`
- **American Express**: `***************`

#### Test Card Details:
- **Expiry**: Any future date (e.g., `12/25`)
- **CVC**: Any 3-digit number (e.g., `123`)
- **ZIP**: Any 5-digit number (e.g., `12345`)

#### Failed Payments (for testing):
- **Declined**: `****************`
- **Insufficient funds**: `****************`
- **Expired card**: `****************`

## API Endpoints Used

### Checkout APIs:
- `POST /store/carts/{id}` - Update cart email/addresses
- `GET /store/shipping-options` - Get shipping options
- `POST /store/carts/{id}/shipping-methods` - Add shipping method
- `POST /store/payment-collections/{id}/payment-sessions` - Initialize payment
- `POST /store/payment-collections/{id}/payment-sessions/{provider}` - Set payment provider
- `POST /store/carts/{id}/complete` - Complete order

### Authentication Required:
All checkout operations require valid JWT token in Authorization header.

## File Structure

```
frontend/src/
├── routes/checkout/
│   └── +page.svelte                 # Main checkout page
├── lib/components/checkout/
│   ├── CheckoutSteps.svelte         # Step indicator
│   ├── EmailStep.svelte             # Email collection
│   ├── AddressStep.svelte           # Address forms
│   ├── ShippingStep.svelte          # Shipping options
│   ├── PaymentStep.svelte           # Payment method selection
│   └── StripePayment.svelte         # Stripe payment form
└── lib/api/client.ts                # API methods for checkout
```

## Key Features

### 1. Multi-Step Checkout
- **Progressive disclosure**: One step at a time
- **Validation**: Each step validated before proceeding
- **Navigation**: Back/forward between steps
- **State management**: Checkout data preserved

### 2. Stripe Integration
- **Secure payment**: PCI-compliant Stripe Elements
- **Test environment**: Safe testing with test cards
- **Error handling**: Clear error messages
- **Payment confirmation**: Real-time payment status

### 3. Order Management
- **Cart completion**: Cart converted to order
- **Order confirmation**: Success page with order details
- **Email notifications**: Order confirmation emails
- **Order history**: Orders appear in customer account

## Troubleshooting

### Common Issues:

1. **"Stripe publishable key not found"**:
   - Check `VITE_STRIPE_PUBLISHABLE_KEY` in frontend `.env.local`
   - Restart frontend development server

2. **"Payment session not found"**:
   - Verify Stripe is configured in backend
   - Check `STRIPE_API_KEY` in backend `.env`
   - Restart backend server

3. **"Failed to initialize payment"**:
   - Ensure cart has items and valid addresses
   - Check browser console for API errors
   - Verify MedusaJS payment provider configuration

4. **"Authentication required"**:
   - User must be logged in for checkout
   - Check JWT token in localStorage
   - Verify auth state in application

### Testing Checklist:

- [ ] Backend running with Stripe configuration
- [ ] Frontend running with Stripe publishable key
- [ ] User authenticated and logged in
- [ ] Cart has items from catalog
- [ ] Can navigate through all checkout steps
- [ ] Can enter test card details
- [ ] Payment processes successfully
- [ ] Order appears in order history

## Production Deployment

### Before Going Live:

1. **Replace Test Keys**:
   - Get live Stripe API keys from dashboard
   - Update environment variables
   - Test with real (small amount) transactions

2. **Webhook Configuration**:
   - Set up Stripe webhooks for payment events
   - Configure webhook endpoint in MedusaJS
   - Test webhook delivery

3. **Security Review**:
   - Ensure HTTPS in production
   - Validate all user inputs
   - Review error handling
   - Test edge cases

## Support

For issues with:
- **Stripe Integration**: Check Stripe documentation and dashboard
- **MedusaJS Checkout**: Review MedusaJS v2 checkout documentation
- **Payment Processing**: Test with different card types and scenarios
- **Order Management**: Verify order completion flow and email notifications

The checkout system is now ready for testing with Stripe test environment!
