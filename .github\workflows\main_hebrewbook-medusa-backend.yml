# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - hebrewbook-medusa-backend

on:
  push:
    branches:
      - main
    paths:
      - 'backend/**'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install dependencies
        run: |
          cd backend
          yarn install

      - name: Build application
        run: |
          cd backend
          echo "🔨 Starting build process..."

          # Ensure clean build environment
          rm -rf .medusa/server .medusa/admin .cache

          yarn build

      - name: Prepare deployment package
        run: |

          echo "🔨 Creating deployment directory..."
          mkdir -p deploy

          echo "📦 Copying files from backend to deploy..."
          cd backend

          # Copy built files and necessary runtime files
          if [ -d ".medusa" ]; then
            echo "✅ Found .medusa directory, copying..."
            cp -r .medusa ../deploy/
          fi

          # Copy essential runtime files to deploy root
          cp startup.sh ../deploy/ || echo "⚠️  No startup.sh found"

          # Make startup.sh executable
          chmod +x ../deploy/startup.sh || echo "⚠️  Could not make startup.sh executable"


      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: ${{ github.workspace }}/deploy
          if-no-files-found: error
          include-hidden-files: true

  deploy:
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app
      
      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'hebrewbook-medusa-backend'
          slot-name: 'Production'
          package: .
          publish-profile: ${{ secrets.AZUREAPPSERVICE_PUBLISHPROFILE_5254DA40BE55475BB8647E5F08E8E065 }}