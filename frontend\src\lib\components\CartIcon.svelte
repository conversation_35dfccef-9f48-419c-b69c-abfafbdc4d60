<script lang="ts">
	import { cartItemCount } from "$lib/stores/cart";
	import { createEventDispatcher } from "svelte";
	import { _ } from '$lib/i18n'

	const dispatch = createEventDispatcher();

	$: itemCount = $cartItemCount;

	function openCart() {
		dispatch("open");
	}
</script>

<button
	type="button"
	class="relative p-2 rounded-md transition-colors hover:bg-black hover:bg-opacity-10"
	on:click={openCart}
	aria-label="Open shopping cart"
>
	<!-- Cart icon -->
	<div class="flex items-center">
		<img src="/images/cart light.png" alt="Shopping cart" class="w-8 h-8" />
		<p style="color: var(--color-text-light)" class="hidden md:block font-medium ml-2">
			{$_('cart.title')}
		</p>
	</div>

	<!-- Item count badge -->
	{#if itemCount > 0}
		<span
			class="absolute -top-1 -right-1 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-body font-medium"
			style="background-color: var(--color-text-accent);"
		>
			{itemCount > 99 ? "99+" : itemCount}
		</span>
	{/if}
</button>
