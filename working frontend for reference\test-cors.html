<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>MedusaJS CORS Test</h1>
    <button onclick="testCors()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCors() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';

            try {
                console.log('Testing CORS with publishable key...');
                
                const response = await fetch('http://localhost:9000/store/regions', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-publishable-api-key': 'pk_a00302b727fcf4f5285975080843c4bb959bc257a8a7e070ea0967c396c64505'
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Success:', data);
                    resultDiv.innerHTML = `
                        <h3 style="color: green;">✅ CORS Test Successful!</h3>
                        <p>Status: ${response.status}</p>
                        <p>Data: ${JSON.stringify(data, null, 2)}</p>
                    `;
                } else {
                    const errorData = await response.text();
                    console.error('Error response:', errorData);
                    resultDiv.innerHTML = `
                        <h3 style="color: red;">❌ Request Failed</h3>
                        <p>Status: ${response.status}</p>
                        <p>Error: ${errorData}</p>
                    `;
                }
            } catch (error) {
                console.error('CORS Error:', error);
                resultDiv.innerHTML = `
                    <h3 style="color: red;">❌ CORS Error</h3>
                    <p>Error: ${error.message}</p>
                    <p>This usually means CORS is not configured correctly.</p>
                `;
            }
        }

        // Test on page load
        window.onload = function() {
            console.log('Page loaded, you can click the button to test CORS');
        };
    </script>
</body>
</html>
