// Debug script to check products and digital products status
// Run with: node debug-products.js

const fetch = require('node-fetch');

const BASE_URL = process.env.ADMIN_URL || 'http://localhost:9000';

async function debugProducts() {
  try {
    console.log('🔍 Debugging Products and Digital Products...\n');

    // Get all products
    console.log('📦 Fetching Products...');
    const productsResponse = await fetch(`${BASE_URL}/admin/products?fields=*variants,*variants.prices`, {
      headers: {
        'Cookie': 'connect.sid=your-session-cookie' // You'll need to get this from browser
      }
    });

    if (!productsResponse.ok) {
      console.log('❌ Failed to fetch products. Make sure you\'re logged in to admin.');
      return;
    }

    const { products } = await productsResponse.json();
    
    console.log(`Found ${products.length} products:\n`);

    for (const product of products) {
      console.log(`📚 Product: ${product.title}`);
      console.log(`   ID: ${product.id}`);
      console.log(`   Status: ${product.status}`);
      console.log(`   Handle: ${product.handle}`);
      
      if (product.variants && product.variants.length > 0) {
        console.log(`   Variants (${product.variants.length}):`);
        for (const variant of product.variants) {
          console.log(`     - ${variant.title || 'Default'}`);
          console.log(`       ID: ${variant.id}`);
          console.log(`       SKU: ${variant.sku || 'No SKU'}`);
          console.log(`       Manage Inventory: ${variant.manage_inventory ? 'ON' : 'OFF'}`);
          console.log(`       Allow Backorders: ${variant.allow_backorder ? 'ON' : 'OFF'}`);
          
          if (variant.prices && variant.prices.length > 0) {
            console.log(`       Prices:`);
            for (const price of variant.prices) {
              console.log(`         ${price.currency_code}: ${price.amount / 100}`);
            }
          } else {
            console.log(`       ❌ No prices set`);
          }
        }
      } else {
        console.log(`   ❌ No variants found`);
      }
      console.log('');
    }

    // Get all digital products
    console.log('\n💾 Fetching Digital Products...');
    const digitalResponse = await fetch(`${BASE_URL}/admin/digital-products?fields=*,*medias,*product_variant`, {
      headers: {
        'Cookie': 'connect.sid=your-session-cookie'
      }
    });

    if (!digitalResponse.ok) {
      console.log('❌ Failed to fetch digital products');
      return;
    }

    const { digital_products } = await digitalResponse.json();
    
    console.log(`Found ${digital_products.length} digital products:\n`);

    for (const dp of digital_products) {
      console.log(`🔗 Digital Product: ${dp.name}`);
      console.log(`   ID: ${dp.id}`);
      console.log(`   Linked to variant: ${dp.product_variant?.id || 'None'}`);
      
      if (dp.medias && dp.medias.length > 0) {
        console.log(`   Media (${dp.medias.length}):`);
        for (const media of dp.medias) {
          console.log(`     - Type: ${media.type}`);
          console.log(`       URL: ${media.url || 'No URL'}`);
          console.log(`       File ID: ${media.fileId || 'No File'}`);
        }
      } else {
        console.log(`   ❌ No media found`);
      }
      console.log('');
    }

    // Check connections
    console.log('\n🔗 Checking Product ↔ Digital Product Connections...');
    for (const product of products) {
      if (product.variants) {
        for (const variant of product.variants) {
          const linkedDigital = digital_products.find(dp => dp.product_variant?.id === variant.id);
          if (linkedDigital) {
            console.log(`✅ ${product.title} (${variant.title}) → ${linkedDigital.name}`);
          } else {
            console.log(`❌ ${product.title} (${variant.title}) → No digital content`);
          }
        }
      }
    }

    console.log('\n📋 Summary:');
    console.log(`   Products: ${products.length}`);
    console.log(`   Digital Products: ${digital_products.length}`);
    console.log(`   Connected: ${digital_products.filter(dp => dp.product_variant).length}`);

  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Instructions
console.log('📝 To use this script:');
console.log('1. Open browser dev tools on admin panel');
console.log('2. Go to Application → Cookies → copy connect.sid value');
console.log('3. Replace "your-session-cookie" above with the actual value');
console.log('4. Run: node debug-products.js\n');

// Uncomment to run (after setting cookie)
// debugProducts();
