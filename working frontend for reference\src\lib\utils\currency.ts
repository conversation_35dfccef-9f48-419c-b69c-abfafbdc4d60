/**
 * Currency formatting utilities
 */

// Currency symbols mapping
const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  ILS: '₪', // Israeli Shekel
  JPY: '¥',
  CAD: 'C$',
  AUD: 'A$',
  CHF: 'CHF',
  CNY: '¥',
  SEK: 'kr',
  NOK: 'kr',
  DKK: 'kr',
  PLN: 'zł',
  CZK: 'Kč',
  HUF: 'Ft',
  RUB: '₽',
  BRL: 'R$',
  INR: '₹',
  KRW: '₩',
  SGD: 'S$',
  HKD: 'HK$',
  MXN: '$',
  ZAR: 'R',
  TRY: '₺',
  THB: '฿'
}

// Currency locale mapping for proper number formatting
const CURRENCY_LOCALES: Record<string, string> = {
  USD: 'en-US',
  EUR: 'de-DE',
  GBP: 'en-GB',
  ILS: 'he-IL', // Hebrew (Israel)
  JPY: 'ja-JP',
  CAD: 'en-CA',
  AUD: 'en-AU',
  CHF: 'de-CH',
  CNY: 'zh-CN',
  SEK: 'sv-SE',
  NOK: 'nb-NO',
  DKK: 'da-DK',
  PLN: 'pl-PL',
  CZK: 'cs-CZ',
  HUF: 'hu-HU',
  RUB: 'ru-RU',
  BRL: 'pt-BR',
  INR: 'en-IN',
  KRW: 'ko-KR',
  SGD: 'en-SG',
  HKD: 'zh-HK',
  MXN: 'es-MX',
  ZAR: 'en-ZA',
  TRY: 'tr-TR',
  THB: 'th-TH'
}

/**
 * Format price with currency symbol and proper locale formatting
 * @param amount - Price amount in cents/smallest currency unit
 * @param currencyCode - Currency code (e.g., 'USD', 'EUR', 'ILS')
 * @param options - Formatting options
 */
export function formatPrice(
  amount: number, 
  currencyCode: string = 'USD',
  options: {
    showSymbol?: boolean
    showCode?: boolean
    locale?: string
  } = {}
): string {
  const {
    showSymbol = true,
    showCode = false,
    locale
  } = options

  console.log("formatPrice called with amount:", amount, "and currencyCode:", currencyCode);
  // Convert from cents to main currency unit
  const mainAmount = amount

  // Get currency info
  const currency = currencyCode.toUpperCase()
  const symbol = CURRENCY_SYMBOLS[currency] || currency
  const defaultLocale = CURRENCY_LOCALES[currency] || 'en-US'
  const formatLocale = locale || defaultLocale

  try {
    // Use Intl.NumberFormat for proper locale formatting
    const formatter = new Intl.NumberFormat(formatLocale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })

    const formatted = formatter.format(mainAmount)
    
    // If we want to show code instead of symbol
    if (showCode && !showSymbol) {
      return `${mainAmount.toFixed(2)} ${currency}`
    }
    
    return formatted
  } catch (error) {
    // Fallback formatting if Intl.NumberFormat fails
    console.warn(`Currency formatting failed for ${currency}:`, error)
    
    if (showSymbol) {
      return `${symbol}${mainAmount.toFixed(2)}`
    } else if (showCode) {
      return `${mainAmount.toFixed(2)} ${currency}`
    } else {
      return mainAmount.toFixed(2)
    }
  }
}

/**
 * Get currency symbol for a currency code
 */
export function getCurrencySymbol(currencyCode: string): string {
  return CURRENCY_SYMBOLS[currencyCode.toUpperCase()] || currencyCode.toUpperCase()
}

/**
 * Check if a currency is right-to-left (for proper display)
 */
export function isCurrencyRTL(currencyCode: string): boolean {
  const rtlCurrencies = ['ILS', 'AED', 'SAR', 'QAR', 'KWD', 'BHD', 'OMR', 'JOD', 'LBP', 'SYP', 'IQD']
  return rtlCurrencies.includes(currencyCode.toUpperCase())
}

/**
 * Format price range (for products with multiple variants)
 */
export function formatPriceRange(
  minAmount: number,
  maxAmount: number,
  currencyCode: string = 'USD'
): string {
  if (minAmount === maxAmount) {
    return formatPrice(minAmount, currencyCode)
  }
  
  const minFormatted = formatPrice(minAmount, currencyCode)
  const maxFormatted = formatPrice(maxAmount, currencyCode)
  
  return `${minFormatted} - ${maxFormatted}`
}
