<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { authStore, isAuthenticated, currentCustomer } from '$lib/stores/auth'
  import { apiClient } from '$lib/api/client'
  import { _ } from '$lib/i18n'
  import { getCurrentLocale, isRTL } from '$lib/i18n'

  let currentLocale = 'en'
  let rtl = false

  // Edit mode state
  let isEditing = false
  let isUpdating = false
  let updateError = ''
  let updateSuccess = false

  // Form data
  let editForm = {
    first_name: '',
    last_name: '',
    phone: ''
  }

  $: currentLocale = getCurrentLocale()
  $: rtl = isRTL(currentLocale)

  // Redirect to home if user logs out
  $: if (!$isAuthenticated && typeof window !== 'undefined') {
    goto('/')
  }

  onMount(() => {
    // Initialize auth state from localStorage
    authStore.initialize()

    // Redirect to home if not authenticated
    if (!$isAuthenticated) {
      goto('/')
    }
  })

  async function handleLogout() {
    await authStore.logout()
    goto('/')
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString(currentLocale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  function startEditing() {
    if ($currentCustomer) {
      editForm = {
        first_name: $currentCustomer.first_name || '',
        last_name: $currentCustomer.last_name || '',
        phone: $currentCustomer.phone || ''
      }
      isEditing = true
      updateError = ''
      updateSuccess = false
    }
  }

  function cancelEditing() {
    isEditing = false
    updateError = ''
    updateSuccess = false
  }

  async function saveChanges() {
    if (!$currentCustomer) return

    try {
      isUpdating = true
      updateError = ''

      // Get the auth token from localStorage
      const token = localStorage.getItem('auth_token')
      if (!token) {
        updateError = 'Authentication token not found. Please sign in again.'
        return
      }

      const response = await apiClient.updateCustomer($currentCustomer.id, {
        first_name: editForm.first_name,
        last_name: editForm.last_name,
        phone: editForm.phone
      }, token)

      if (response.data?.customer) {
        // Update the auth store with new customer data
        authStore.updateCustomer(response.data.customer)
        isEditing = false
        updateSuccess = true

        // Hide success message after 3 seconds
        setTimeout(() => {
          updateSuccess = false
        }, 3000)
      } else if (response.error) {
        updateError = response.error
      }
    } catch (error) {
      console.error('Failed to update customer:', error)
      updateError = 'Failed to update information. Please try again.'
    } finally {
      isUpdating = false
    }
  }
</script>

<svelte:head>
  <title>{$_('account.page_title')} - Hebrew Book Store</title>
  <meta name="description" content={$_('account.page_description')} />
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    {#if $isAuthenticated && $currentCustomer}
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="font-book-title text-3xl md:text-4xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('account.welcome')}, {$currentCustomer.first_name || $currentCustomer.email}!
        </h1>
        <p class="font-book-text text-lg" style="color: var(--color-text-secondary);">
          {$_('account.manage_your_account')}
        </p>
      </div>

      <!-- Account Information Card -->
      <div class="card-classic mb-8">
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="font-book-title text-xl font-bold" style="color: var(--color-text-primary);">
              {$_('account.account_information')}
            </h2>
            <div class="flex space-x-2">
              {#if !isEditing}
                <button
                  on:click={startEditing}
                  class="px-4 py-2 font-book-text text-sm border rounded-md transition-colors hover:bg-opacity-10"
                  style="color: var(--color-primary); border-color: var(--color-primary); hover:background: var(--color-primary);"
                >
                  {$_('account.edit_profile')}
                </button>
              {/if}
              <button
                on:click={handleLogout}
                class="px-4 py-2 font-book-text text-sm border rounded-md transition-colors hover:bg-opacity-10"
                style="color: #dc2626; border-color: #dc2626; hover:background: #dc2626;"
              >
                {$_('auth.sign_out')}
              </button>
            </div>
          </div>

          <!-- Success/Error Messages -->
          {#if updateSuccess}
            <div class="mb-4 p-3 rounded-md" style="background: #dcfce7; color: #166534; border: 1px solid #bbf7d0;">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {$_('account.profile_updated')}
              </div>
            </div>
          {/if}

          {#if updateError}
            <div class="mb-4 p-3 rounded-md" style="background: #fef2f2; color: #dc2626; border: 1px solid #fecaca;">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                {updateError}
              </div>
            </div>
          {/if}

          <div class="grid md:grid-cols-2 gap-6">
            <!-- Personal Information -->
            <div>
              <div class="flex items-center justify-between mb-4">
                <h3 class="font-book-title text-lg font-semibold" style="color: var(--color-text-primary);">
                  {$_('account.personal_information')}
                </h3>
                {#if isEditing}
                  <div class="flex space-x-2">
                    <button
                      on:click={saveChanges}
                      disabled={isUpdating}
                      class="px-3 py-1 font-book-text text-sm rounded-md transition-colors"
                      class:opacity-50={isUpdating}
                      style="background: var(--color-primary); color: white;"
                    >
                      {#if isUpdating}
                        <div class="flex items-center">
                          <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                          {$_('account.saving')}
                        </div>
                      {:else}
                        {$_('account.save')}
                      {/if}
                    </button>
                    <button
                      on:click={cancelEditing}
                      disabled={isUpdating}
                      class="px-3 py-1 font-book-text text-sm border rounded-md transition-colors"
                      style="color: var(--color-text-secondary); border-color: var(--color-border);"
                    >
                      {$_('account.cancel')}
                    </button>
                  </div>
                {/if}
              </div>

              {#if isEditing}
                <!-- Edit Form -->
                <form on:submit|preventDefault={saveChanges} class="space-y-4">
                  <div>
                    <label for="email" class="block font-book-text text-sm font-medium mb-1" style="color: var(--color-text-secondary);">
                      {$_('auth.email')}
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={$currentCustomer.email}
                      disabled
                      class="w-full px-3 py-2 border rounded-md font-book-text text-sm opacity-50"
                      style="background: var(--color-bg-subtle); border-color: var(--color-border); color: var(--color-text-secondary);"
                    />
                    <p class="text-xs mt-1 font-book-text" style="color: var(--color-text-accent);">
                      {$_('account.email_cannot_be_changed')}
                    </p>
                  </div>

                  <div>
                    <label for="first_name" class="block font-book-text text-sm font-medium mb-1" style="color: var(--color-text-secondary);">
                      {$_('auth.first_name')}
                    </label>
                    <input
                      id="first_name"
                      type="text"
                      bind:value={editForm.first_name}
                      class="w-full px-3 py-2 border rounded-md font-book-text text-sm focus:ring-2 focus:ring-opacity-50"
                      style="background: var(--color-bg-primary); border-color: var(--color-border); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
                      placeholder={$_('auth.first_name_placeholder')}
                    />
                  </div>

                  <div>
                    <label for="last_name" class="block font-book-text text-sm font-medium mb-1" style="color: var(--color-text-secondary);">
                      {$_('auth.last_name')}
                    </label>
                    <input
                      id="last_name"
                      type="text"
                      bind:value={editForm.last_name}
                      class="w-full px-3 py-2 border rounded-md font-book-text text-sm focus:ring-2 focus:ring-opacity-50"
                      style="background: var(--color-bg-primary); border-color: var(--color-border); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
                      placeholder={$_('auth.last_name_placeholder')}
                    />
                  </div>

                  <div>
                    <label for="phone" class="block font-book-text text-sm font-medium mb-1" style="color: var(--color-text-secondary);">
                      {$_('auth.phone')}
                    </label>
                    <input
                      id="phone"
                      type="tel"
                      bind:value={editForm.phone}
                      class="w-full px-3 py-2 border rounded-md font-book-text text-sm focus:ring-2 focus:ring-opacity-50"
                      style="background: var(--color-bg-primary); border-color: var(--color-border); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
                      placeholder={$_('auth.phone_placeholder')}
                    />
                  </div>
                </form>
              {:else}
                <!-- View Mode -->
                <div class="space-y-3">
                  <div>
                    <span class="block font-book-text text-sm font-medium" style="color: var(--color-text-secondary);">
                      {$_('auth.email')}
                    </span>
                    <p class="font-book-text" style="color: var(--color-text-primary);">
                      {$currentCustomer.email}
                    </p>
                  </div>

                  <div>
                    <span class="block font-book-text text-sm font-medium" style="color: var(--color-text-secondary);">
                      {$_('auth.first_name')}
                    </span>
                    <p class="font-book-text" style="color: var(--color-text-primary);">
                      {$currentCustomer.first_name || $_('account.not_provided')}
                    </p>
                  </div>

                  <div>
                    <span class="block font-book-text text-sm font-medium" style="color: var(--color-text-secondary);">
                      {$_('auth.last_name')}
                    </span>
                    <p class="font-book-text" style="color: var(--color-text-primary);">
                      {$currentCustomer.last_name || $_('account.not_provided')}
                    </p>
                  </div>

                  <div>
                    <span class="block font-book-text text-sm font-medium" style="color: var(--color-text-secondary);">
                      {$_('auth.phone')}
                    </span>
                    <p class="font-book-text" style="color: var(--color-text-primary);">
                      {$currentCustomer.phone || $_('account.not_provided')}
                    </p>
                  </div>
                </div>
              {/if}
            </div>

            <!-- Account Details -->
            <div>
              <h3 class="font-book-title text-lg font-semibold mb-4" style="color: var(--color-text-primary);">
                {$_('account.account_details')}
              </h3>
              <div class="space-y-3">
                <div>
                  <span class="block font-book-text text-sm font-medium" style="color: var(--color-text-secondary);">
                    {$_('account.member_since')}
                  </span>
                  <p class="font-book-text" style="color: var(--color-text-primary);">
                    {formatDate($currentCustomer.created_at)}
                  </p>
                </div>

                <div>
                  <span class="block font-book-text text-sm font-medium" style="color: var(--color-text-secondary);">
                    {$_('account.last_updated')}
                  </span>
                  <p class="font-book-text" style="color: var(--color-text-primary);">
                    {formatDate($currentCustomer.updated_at)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid md:grid-cols-3 gap-6">
        <!-- My Library -->
        <a
          href="/library"
          class="card-classic p-6 text-center hover:shadow-lg transition-shadow"
        >
          <div class="text-4xl mb-4">📖</div>
          <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
            {$_('library.my_library')}
          </h3>
          <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
            {$_('library.read_purchased_books')}
          </p>
        </a>

        <!-- My Orders -->
        <a
          href="/orders"
          class="card-classic p-6 text-center hover:shadow-lg transition-shadow"
        >
          <div class="text-4xl mb-4">📦</div>
          <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
            {$_('orders.my_orders')}
          </h3>
          <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
            {$_('orders.view_purchase_history')}
          </p>
        </a>

      </div>

    {:else}
      <!-- Not Authenticated -->
      <div class="text-center py-16">
        <div class="text-6xl mb-6">🔒</div>
        <h1 class="font-book-title text-2xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('account.access_required')}
        </h1>
        <p class="font-book-text text-lg mb-8" style="color: var(--color-text-secondary);">
          {$_('account.please_sign_in')}
        </p>
        <a
          href="/"
          class="btn-classic inline-block"
        >
          {$_('account.go_home')}
        </a>
      </div>
    {/if}
  </div>
</div>
