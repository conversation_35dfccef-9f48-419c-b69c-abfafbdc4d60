{"name": "hebrew-book-store", "version": "1.0.0", "description": "Hebrew Book Store - Headless E-commerce Platform", "private": true, "scripts": {"dev:backend": "cd backend && yarn dev", "dev:frontend": "cd frontend && yarn dev", "build:backend": "cd backend && yarn build", "build:frontend": "cd frontend && yarn build", "build": "npm run build:backend && npm run build:frontend", "start:backend": "cd backend && yarn start", "start:frontend": "cd frontend && yarn preview", "test:backend": "cd backend && yarn test", "test:frontend": "cd frontend && yarn test", "test": "npm run test:backend && npm run test:frontend", "setup:test": "powershell -ExecutionPolicy Bypass -File scripts/setup-test-env.ps1", "setup:test:bash": "bash scripts/setup-test-env.sh", "init:db": "docker exec -i hebrew-book-store-postgres psql -U medusa_user -d hebrew_book_clean < scripts/init-test-data.sql"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/hebrew-book-store.git"}, "keywords": ["hebrew", "e-commerce", "medusa", "sveltekit", "typescript", "multilingual", "rtl", "headless"], "author": "Your Name", "license": "MIT", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}