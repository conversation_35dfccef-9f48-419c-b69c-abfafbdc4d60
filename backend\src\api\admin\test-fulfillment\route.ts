import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"
import { Modules } from "@medusajs/framework/utils"

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const fulfillmentModuleService = req.scope.resolve(Modules.FULFILLMENT)

  try {
    console.log('🔍 Testing fulfillment module...')

    // List shipping profiles
    const shippingProfiles = await fulfillmentModuleService.listShippingProfiles()
    console.log('📦 Available shipping profiles:', shippingProfiles.length)

    // List shipping options
    const shippingOptions = await fulfillmentModuleService.listShippingOptions()
    console.log('🚚 Available shipping options:', shippingOptions.length)

    // List fulfillment providers
    const providers = await fulfillmentModuleService.listFulfillmentProviders()
    console.log('🏭 Available fulfillment providers:', providers.length)

    res.json({
      success: true,
      fulfillment_module_connected: true,
      shipping_profiles: shippingProfiles,
      shipping_options: shippingOptions,
      fulfillment_providers: providers,
      message: "Fulfillment module is properly connected and working",
    })
  } catch (error) {
    console.error('❌ Fulfillment module test failed:', error)
    res.status(500).json({
      success: false,
      fulfillment_module_connected: false,
      error: error.message,
      message: "Fulfillment module is not working properly",
    })
  }
}
