# 📄 PDF.js Implementation Guide

This document explains the PDF.js integration alongside the existing FlipHTML5 support in the Hebrew Book Store.

## 🎯 Overview

The system now supports **two types of book readers**:

1. **📖 FlipHTML5 Reader** - For interactive flipbook experiences (existing)
2. **📄 PDF.js Reader** - For native PDF viewing with advanced controls (new)

The system **automatically detects** the content type and uses the appropriate reader.

## 🏗️ Architecture

### **Components Structure**

```
frontend/src/lib/components/
├── PDFViewer.svelte          # PDF.js implementation
├── BookReader.svelte         # Unified reader component
└── (existing components)
```

### **Detection Logic**

The system detects reader type based on URL patterns:

```typescript
function detectReaderType(book: any): 'pdf' | 'fliphtml5' | 'none' {
  const url = book.medias?.find(m => m.type === 'main')?.url || book.fliphtml5_url
  
  if (url.toLowerCase().includes('.pdf')) return 'pdf'
  if (url.includes('fliphtml5.com')) return 'fliphtml5'
  
  return 'fliphtml5' // Default for backward compatibility
}
```

## 📄 PDF.js Features

### **Core Features**
- ✅ **Native PDF rendering** using Mozilla PDF.js
- ✅ **Page navigation** (previous/next, jump to page)
- ✅ **Zoom controls** (fit width, fit height, custom zoom levels)
- ✅ **Download support** 
- ✅ **Print support** (opens in new tab)
- ✅ **Responsive design** with mobile support
- ✅ **Loading states** and error handling

### **Toolbar Controls**
- **Navigation**: ← → buttons, page counter
- **Zoom**: Dropdown with fit options and percentage levels
- **Actions**: Download and open in new tab buttons

### **Zoom Options**
- `page-width` - Fit to container width
- `page-height` - Fit to container height  
- `page-fit` - Fit entire page
- `50%`, `75%`, `100%`, `125%`, `150%`, `200%` - Fixed percentages

## 🔧 Usage Examples

### **For PDF Files**

```json
// Digital Product Media Configuration
{
  "type": "main",
  "url": "https://example.com/book.pdf",
  "title": "Complete Book"
}
```

### **For FlipHTML5 (Existing)**

```json
// Digital Product Media Configuration  
{
  "type": "main",
  "url": "https://online.fliphtml5.com/xrcwd/qlyk/",
  "title": "Interactive Book"
}
```

## 🎨 Component API

### **PDFViewer.svelte**

```svelte
<PDFViewer
  pdfUrl="https://example.com/book.pdf"
  title="Book Title"
  height="600px"
  initialPage={1}
  initialZoom="page-width"
  showToolbar={true}
  enableSearch={true}
  enableDownload={true}
  enablePrint={true}
/>
```

**Props:**
- `pdfUrl` - URL to the PDF file
- `title` - Document title for accessibility
- `height` - Container height (default: "600px")
- `initialPage` - Starting page number (default: 1)
- `initialZoom` - Initial zoom level (default: "page-width")
- `showToolbar` - Show/hide toolbar (default: true)
- `enableSearch` - Enable search functionality (default: true)
- `enableDownload` - Show download button (default: true)
- `enablePrint` - Show print/open button (default: true)

### **BookReader.svelte**

```svelte
<BookReader
  book={bookObject}
  title="Book Title"
  height="600px"
  isLoading={false}
/>
```

**Props:**
- `book` - Book object with medias array
- `title` - Display title
- `height` - Container height
- `isLoading` - Loading state

## 🚀 Setup & Installation

### **Dependencies Added**

```json
{
  "dependencies": {
    "pdfjs-dist": "^3.11.174"
  }
}
```

### **CDN Worker**

The implementation uses CDN for the PDF.js worker:
```javascript
pdfjsLib.GlobalWorkerOptions.workerSrc = 
  'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
```

## 📱 Mobile Support

The PDF viewer is fully responsive:

- **Mobile**: Simplified toolbar, touch-friendly controls
- **Tablet**: Full toolbar with optimized spacing
- **Desktop**: Complete feature set with hover states

## 🔍 Testing

### **Test PDF Available**

A test PDF is available at `/test-sample.pdf` for development testing.

### **Test Different Content Types**

1. **PDF Test**: Use any `.pdf` URL
2. **FlipHTML5 Test**: Use existing FlipHTML5 URLs
3. **Mixed Library**: Test with both types in the same library

## 🎯 Benefits

### **For PDF Content**
- ✅ **Better Performance** - Native browser rendering
- ✅ **Offline Capable** - Works without external services
- ✅ **Customizable** - Full control over UI and features
- ✅ **Accessible** - Screen reader compatible
- ✅ **Mobile Optimized** - Touch-friendly interface

### **For FlipHTML5 Content**
- ✅ **Preserved** - All existing functionality maintained
- ✅ **Interactive** - Flip animations and effects
- ✅ **Rich Media** - Embedded videos, audio, links

## 🔄 Migration Path

### **Existing Books**
- ✅ **No Changes Required** - FlipHTML5 books continue working
- ✅ **Automatic Detection** - System chooses correct reader
- ✅ **Backward Compatible** - All existing URLs supported

### **New PDF Books**
1. Upload PDF to your hosting/CDN
2. Create digital product with PDF URL
3. System automatically uses PDF.js reader

## 🛠️ Customization

### **Styling**

The PDF viewer uses CSS custom properties:

```css
.pdf-viewer-container {
  --color-bg-primary: /* Background color */
  --color-bg-secondary: /* Toolbar background */
  --color-border: /* Border color */
  --color-text-primary: /* Text color */
  --color-primary: /* Accent color */
}
```

### **Feature Toggles**

Disable features per book:

```svelte
<PDFViewer
  pdfUrl={url}
  enableDownload={false}  <!-- Disable download -->
  enablePrint={false}     <!-- Disable print -->
  showToolbar={false}     <!-- Hide toolbar -->
/>
```

## 🔒 Security Considerations

### **CORS Requirements**
- PDF files must be served with proper CORS headers
- Use same-origin URLs when possible
- Test cross-origin PDFs thoroughly

### **Content Security Policy**
- Allow `blob:` URLs for PDF.js worker
- Allow CDN domains for worker script

## 📊 Performance

### **Loading Optimization**
- ✅ **Lazy Loading** - PDF.js loaded only when needed
- ✅ **Progressive Rendering** - Pages render as they load
- ✅ **Memory Management** - Automatic cleanup on component destroy

### **Bundle Size**
- PDF.js core: ~500KB (loaded dynamically)
- Worker: ~1.5MB (loaded from CDN)
- Total impact: Minimal on initial page load

## 🐛 Troubleshooting

### **Common Issues**

1. **PDF Not Loading**
   - Check CORS headers on PDF server
   - Verify PDF URL is accessible
   - Check browser console for errors

2. **Worker Errors**
   - Ensure CDN worker URL is accessible
   - Check Content Security Policy settings

3. **Mobile Issues**
   - Test touch gestures
   - Verify responsive layout
   - Check viewport meta tag

### **Debug Mode**

Enable console logging:
```javascript
console.log('📄 PDF loaded successfully. Pages:', totalPages)
console.log('📄 Detected reader type:', readerType)
```

## 🚀 Future Enhancements

### **Planned Features**
- 🔍 **Text Search** - Full-text search within PDFs
- 📝 **Annotations** - Highlight and note-taking
- 🔖 **Bookmarks** - Save reading positions
- 📱 **Offline Mode** - Cache PDFs for offline reading
- 🎨 **Themes** - Dark mode and custom themes

### **Advanced Integration**
- 📊 **Reading Analytics** - Track reading progress
- 🔄 **Sync** - Cross-device reading position sync
- 📚 **Collections** - Organize PDFs into collections

---

## 📚 Resources

- [PDF.js Documentation](https://mozilla.github.io/pdf.js/)
- [PDF.js API Reference](https://mozilla.github.io/pdf.js/api/)
- [SvelteKit Documentation](https://kit.svelte.dev/)

---

**The PDF.js implementation provides a modern, accessible, and feature-rich reading experience while maintaining full backward compatibility with existing FlipHTML5 content.** 🎉
