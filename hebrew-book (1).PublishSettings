<publishData><publishProfile profileName="hebrew-book - Web Deploy" publishMethod="MSDeploy" publishUrl="hebrew-book.scm.azurewebsites.net:443" msdeploySite="hebrew-book" userName="$hebrew-book" userPWD="6iQYpxFewip74nuK8ckWiGZYFqzdawGSLJknvjf9lLx0gr9x3qkM5EhdjCT1" destinationAppUrl="https://hebrew-book.azurewebsites.net" SQLServerDBConnectionString="" mySQLDBConnectionString="" hostingProviderForumLink="" controlPanelLink="https://portal.azure.com" webSystem="WebSites"><databases /></publishProfile><publishProfile profileName="hebrew-book - FTP" publishMethod="FTP" publishUrl="ftps://waws-prod-ilc-003.ftp.azurewebsites.windows.net/site/wwwroot" ftpPassiveMode="True" userName="REDACTED" userPWD="REDACTED" destinationAppUrl="https://hebrew-book.azurewebsites.net" SQLServerDBConnectionString="REDACTED" mySQLDBConnectionString="" hostingProviderForumLink="" controlPanelLink="https://portal.azure.com" webSystem="WebSites"><databases /></publishProfile><publishProfile profileName="hebrew-book - Zip Deploy" publishMethod="ZipDeploy" publishUrl="hebrew-book.scm.azurewebsites.net:443" userName="$hebrew-book" userPWD="6iQYpxFewip74nuK8ckWiGZYFqzdawGSLJknvjf9lLx0gr9x3qkM5EhdjCT1" destinationAppUrl="https://hebrew-book.azurewebsites.net" SQLServerDBConnectionString="" mySQLDBConnectionString="" hostingProviderForumLink="" controlPanelLink="https://portal.azure.com" webSystem="WebSites"><databases /></publishProfile></publishData>