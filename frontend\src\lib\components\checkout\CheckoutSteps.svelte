<script lang="ts">
  import { _ } from '$lib/i18n'

  export let currentStep: number = 1

  const steps = [
    { number: 1, key: 'email', icon: '📧' },
    { number: 2, key: 'billing', icon: '📍' },
    { number: 3, key: 'payment', icon: '💳' }
  ]

  function getStepStatus(stepNumber: number) {
    if (stepNumber < currentStep) return 'completed'
    if (stepNumber === currentStep) return 'current'
    return 'upcoming'
  }
</script>

<div class="mb-8">
  <div class="flex items-center justify-between">
    {#each steps as step, index (step.number)}
      <div class="flex items-center {index < steps.length - 1 ? 'flex-1' : ''}">
        <!-- Step Circle -->
        <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
          {getStepStatus(step.number) === 'completed' 
            ? 'bg-green-500 border-green-500 text-white' 
            : getStepStatus(step.number) === 'current'
            ? 'border-blue-500 text-blue-500 bg-blue-50'
            : 'border-gray-300 text-gray-400 bg-white'}"
        >
          {#if getStepStatus(step.number) === 'completed'}
            ✓
          {:else}
            <span class="text-lg">{step.icon}</span>
          {/if}
        </div>

        <!-- Step Label -->
        <div class="ml-3 min-w-0 flex-1">
          <p class="text-sm font-medium transition-colors duration-300
            {getStepStatus(step.number) === 'completed' 
              ? 'text-green-600' 
              : getStepStatus(step.number) === 'current'
              ? 'text-blue-600'
              : 'text-gray-500'}"
            style="font-family: var(--font-book-text);"
          >
            {$_(`checkout.step_${step.key}`)}
          </p>
        </div>

        <!-- Connector Line -->
        {#if index < steps.length - 1}
          <div class="flex-1 mx-4">
            <div class="h-0.5 transition-colors duration-300
              {step.number < currentStep ? 'bg-green-500' : 'bg-gray-300'}"
            ></div>
          </div>
        {/if}
      </div>
    {/each}
  </div>
</div>

<style>
  /* Responsive adjustments for mobile */
  @media (max-width: 640px) {
    .flex-1 {
      min-width: 0;
    }
  }
</style>
