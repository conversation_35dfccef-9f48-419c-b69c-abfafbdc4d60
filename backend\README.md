# Hebrew Book Store - Backend (MedusaJS)

This is the **independent backend API server** for the Hebrew Book Store e-commerce platform, built with MedusaJS v2.

## 🔧 Backend Features

- **🏪 E-commerce API**: Complete store management with products, orders, customers
- **📚 Digital Products**: Automatic fulfillment system with FlipHTML5 integration
- **👥 Customer Management**: Registration, authentication, order tracking
- **💳 Payment Processing**: Stripe integration for secure payments
- **⚙️ Admin Panel**: Full-featured admin interface at `/app`
- **🔌 RESTful API**: All endpoints for frontend consumption
- **📧 Notifications**: Email system for order confirmations and digital delivery

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+
- **PostgreSQL** database
- **Yarn** package manager

### Installation

```bash
# Install dependencies
yarn install

# Configure environment variables
cp .env.example .env
# Edit .env with your database credentials and API keys

# Run database migrations
yarn medusa db:migrate

# Start development server
yarn dev
```

### Access Points

- **🔌 API Server**: http://localhost:9000
- **⚙️ Admin Panel**: http://localhost:9000/app
- **📊 Health Check**: http://localhost:9000/health

## 📁 Project Structure

```
backend/
├── 📂 src/
│   ├── 📂 api/                 # API routes
│   │   ├── 📂 admin/          # Admin-only endpoints
│   │   ├── 📂 store/          # Store/customer endpoints
│   │   └── 📂 public/         # Public endpoints
│   ├── 📂 modules/            # Custom modules
│   │   ├── 📂 digital-product/ # Digital product management
│   │   ├── 📂 book/           # Book-specific logic
│   │   └── 📂 notification/   # Email notifications
│   ├── 📂 subscribers/        # Event subscribers
│   ├── 📂 workflows/          # Business logic workflows
│   └── 📂 links/              # Module relationships
├── 📄 medusa-config.ts        # MedusaJS configuration
├── 📄 package.json            # Dependencies
└── 📄 .env                    # Environment variables
```

## 🔌 API Endpoints

### Store API (Customer-facing)
- `GET /store/products` - List products
- `GET /store/customers/me` - Customer profile
- `GET /store/customers/me/digital-products` - Customer's digital products
- `POST /store/carts` - Create cart
- `POST /store/carts/{id}/complete` - Complete order

### Admin API (Admin-only)
- `GET /admin/products` - Manage products
- `POST /admin/digital-products` - Create digital products
- `GET /admin/customers` - Manage customers
- `GET /admin/orders` - Manage orders

### Public API (No auth required)
- `GET /public/settings` - Store settings
- `GET /health` - Health check

## 🛠️ Development

### Available Scripts

```bash
yarn dev                    # Start development server with hot reload
yarn build                  # Build for production
yarn start                  # Start production server
yarn test                   # Run tests
yarn medusa db:migrate      # Run database migrations
yarn medusa db:generate     # Generate new migrations
```

### Environment Variables

Create a `.env` file with:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/medusa_db

# Redis (optional, uses in-memory if not provided)
REDIS_URL=redis://localhost:6379

# JWT Secret
JWT_SECRET=your-jwt-secret

# Admin CORS
ADMIN_CORS=http://localhost:9000,http://localhost:7001

# Store CORS  
STORE_CORS=http://localhost:5173,http://localhost:3000

# Stripe (for payments)
STRIPE_API_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email (optional)
SENDGRID_API_KEY=SG...
```

## 📚 Digital Products System

This backend includes a complete digital products system:

### Features
- **Automatic Fulfillment**: Orders with digital products are automatically fulfilled
- **FlipHTML5 Integration**: Store FlipHTML5 URLs for book content
- **Email Delivery**: Customers receive digital content links via email
- **Admin Management**: Configure digital content through admin panel

### Configuration
Digital products are configured using product metadata:

```json
{
  "is_digital_product": "true",
  "digital_medias": "[{\"type\":\"main\",\"url\":\"https://online.fliphtml5.com/...\",\"title\":\"Book Title\"}]"
}
```

See [Digital Products Setup Guide](../DIGITAL_PRODUCTS_SETUP.md) for detailed instructions.

## 🔧 Custom Modules

### Digital Product Module
- **Models**: DigitalProduct, DigitalProductMedia, DigitalProductOrder
- **Service**: CRUD operations for digital products
- **Workflows**: Automatic fulfillment on order completion

### Book Module  
- **Enhanced product management** for book-specific features
- **Chapter organization** using collections
- **Metadata handling** for book properties

### Notification Module
- **Email templates** for order confirmations
- **Digital product delivery** notifications
- **Customizable messaging** system

## 🚀 Deployment

### Production Build

```bash
# Build the application
yarn build

# Start production server
yarn start
```

### Environment Setup
- Configure production database
- Set up Redis for caching
- Configure email provider
- Set production CORS origins

### Recommended Hosting
- **Railway** - Easy PostgreSQL + Node.js hosting
- **Heroku** - Classic platform with add-ons
- **DigitalOcean** - App Platform or Droplets
- **AWS** - ECS, Lambda, or EC2

## 🧪 Testing

```bash
# Run all tests
yarn test

# Run specific test file
yarn test src/modules/digital-product

# Run tests in watch mode
yarn test --watch
```

## 📖 Documentation

- [MedusaJS Documentation](https://docs.medusajs.com)
- [Digital Products Setup](../DIGITAL_PRODUCTS_SETUP.md)
- [Admin Configuration](../ADMIN_CONFIGURATION_GUIDE.md)
- [API Reference](https://docs.medusajs.com/api/store)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**This backend is completely independent and can be deployed separately from the frontend!**
