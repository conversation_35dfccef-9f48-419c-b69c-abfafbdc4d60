# 📚 Digital Products Setup Guide

## Overview
This guide explains how to configure digital products (books, chapters) for automatic delivery through the MedusaJS admin panel using FlipHTML5 integration.

## 🎯 What You'll Achieve
- **Automatic digital product delivery** after payment
- **FlipHTML5 integration** for book reading
- **Admin panel management** (no API calls needed)
- **Customer library access** with purchased content

---

## 📋 Step-by-Step Setup

### Step 1: Create Regular Product in Admin Panel

1. **Login to Admin Panel** → Go to `http://localhost:9000/app`
2. **Navigate to Products** → Click "Products" in sidebar
3. **Add New Product** → Click "Add Product" button
4. **Fill Product Details**:
   - **Title**: e.g., "Hebrew Grammar Complete Course"
   - **Description**: Detailed description of the book/course
   - **Images**: Upload book cover or preview images
   - **Type**: Set to "Book" or "Chapter"
5. **Create Product Variant**:
   - Set pricing (e.g., $29.99)
   - Configure inventory if needed
6. **Save Product**

### Step 2: Configure Digital Content via Metadata

1. **Open the product** you just created
2. **Scroll down to "Metadata" section**
3. **Add the following metadata fields**:

#### Required Metadata Fields:

**Field 1: `is_digital_product`**
```
Key: is_digital_product
Value: true
```

**Field 2: `digital_medias`**
```
Key: digital_medias
Value: [{"type":"preview","url":"https://online.fliphtml5.com/xrcwd/pogf/","title":"Free Preview"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/qvsl/","title":"Complete Book"}]
```
https://online.fliphtml5.com/xrcwd/qvsl/
---

## 🔧 FlipHTML5 URL Configuration

### Getting FlipHTML5 URLs:

1. **Login to FlipHTML5** → Go to your FlipHTML5 account
2. **Open your book project**
3. **Click "Share" button**
4. **Click "Embed" tab**
5. **Copy the iframe src URL**:
   ```html
   <iframe src="https://online.fliphtml5.com/xrcwd/qlyk/" width="100%" height="600"></iframe>
   ```
6. **Extract the URL**: `https://online.fliphtml5.com/xrcwd/qlyk/`

### Content Types:

#### Preview Content (Free Sample)
- **Type**: `preview`
- **Purpose**: Free sample for customers to preview before purchase
- **Example**: First chapter or limited pages

#### Main Content (Full Access)
- **Type**: `main`
- **Purpose**: Complete book access after purchase
- **Example**: All chapters and content

---

## 📝 Metadata Examples

### Single Digital Content (Main Only)
```json
[{"type":"main","url":"https://online.fliphtml5.com/xrcwd/qlyk/","title":"Hebrew Grammar Book"}]
```

### Multiple Contents (Preview + Main)
```json
[{"type":"preview","url":"https://online.fliphtml5.com/xrcwd/preview/","title":"Free Preview","description":"First 3 chapters"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/full/","title":"Complete Book","description":"All 12 chapters with exercises"}]
```

### Complex Example with Multiple Files
```json
[{"type":"preview","url":"https://online.fliphtml5.com/xrcwd/preview/","title":"Sample Chapter"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/book1/","title":"Part 1: Basics"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/book2/","title":"Part 2: Advanced"}]
```

---

## 🚀 Automatic Fulfillment Process

### What Happens When Customer Orders:

1. **Customer places order** with digital products
2. **Payment is processed** successfully
3. **System detects** products with `is_digital_product: true`
4. **Email notification sent** automatically with access links
5. **Products appear** in customer's library page
6. **Customer can read** books immediately via FlipHTML5 integration

### Expected System Logs:
```
🔔 Order placed event received: order_123...
📦 Processing order: order_123...
📚 Found digital product: Hebrew Grammar Book
📧 Sending digital product notification for 1 products
✅ Digital order fulfillment completed for order: order_123...
```

---

## 📚 Customer Experience

### Library Page Access:
- **URL**: `http://localhost:3000/library`
- **Authentication**: Customer must be logged in
- **Content**: Shows all purchased digital products
- **Reading**: Click "Read Now" to open FlipHTML5 viewer

### What Customers See:
1. **Before Purchase**: Preview content (if configured)
2. **After Purchase**: Full content automatically available
3. **Library Page**: List of all purchased books
4. **Embedded Reader**: FlipHTML5 iframe for seamless reading

---

## ✅ Setup Checklist

### For Each Digital Product:
- [ ] Create product in admin panel
- [ ] Add product images and description
- [ ] Set appropriate pricing
- [ ] Add `is_digital_product: true` metadata
- [ ] Add `digital_medias` JSON with FlipHTML5 URLs
- [ ] Test with a sample order
- [ ] Verify email notification is sent
- [ ] Check customer library access

### System Requirements:
- [ ] MedusaJS backend running
- [ ] Frontend application running
- [ ] Email notification system configured
- [ ] FlipHTML5 account with published books
- [ ] Customer authentication working

---

## 🔧 Troubleshooting

### Common Issues:

**Digital products not appearing in library:**
- Check `is_digital_product` metadata is set to `"true"`
- Verify `digital_medias` JSON is valid
- Ensure customer is logged in

**FlipHTML5 not loading:**
- Verify FlipHTML5 URLs are correct and accessible
- Check if books are published in FlipHTML5
- Ensure URLs don't have extra parameters

**Email notifications not sent:**
- Check backend logs for notification errors
- Verify email provider configuration
- Ensure order status is "completed"

### Testing:
1. Create a test product with digital content
2. Place a test order
3. Check backend logs for processing
4. Verify email is sent
5. Login as customer and check library

---

## 📞 Support

If you encounter issues:
1. Check backend console logs
2. Verify all metadata fields are correct
3. Test FlipHTML5 URLs directly in browser
4. Ensure customer authentication is working
5. Check email provider configuration

---

**🎉 Your digital product system is now ready for automatic fulfillment!**
