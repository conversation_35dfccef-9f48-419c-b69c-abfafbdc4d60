# Hebrew Book Store - Headless E-commerce Platform

A modern, headless e-commerce platform for Hebrew books built with **MedusaJS** (backend) and **SvelteKit** (frontend) as completely separate applications.

## 🏗️ Headless Architecture

This project follows a **true headless architecture**:

- **🔧 Backend**: Independent MedusaJS API server
- **🎨 Frontend**: Independent SvelteKit application  
- **📡 Communication**: RESTful API calls between frontend and backend
- **🚀 Deployment**: Each application can be deployed separately
- **🔄 Scalability**: Scale frontend and backend independently

## ✨ Features

- **🌐 Multilingual Support**: Hebrew (RTL) and English (LTR) with automatic language detection
- **📚 Digital Products**: Automatic fulfillment with FlipHTML5 integration
- **💳 Payment Integration**: Stripe payment processing
- **👥 Customer Management**: Registration, authentication, and order tracking
- **📖 Library System**: Customer digital library with purchased content
- **⚙️ Admin Panel**: Full-featured admin interface for content management
- **📱 Responsive Design**: Mobile-first approach with Tailwind CSS

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+
- **PostgreSQL** database
- **Yarn** package manager

### 1. Clone Repository

```bash
git clone https://github.com/your-username/hebrew-book-store.git
cd hebrew-book-store
```

### 2. Setup Backend (MedusaJS)

```bash
cd backend

# Install dependencies
yarn install

# Configure environment
cp .env.example .env
# Edit .env with your database and API keys

# Run migrations
yarn medusa db:migrate

# Start backend server
yarn dev
```

**Backend will run on**: `http://localhost:9000`
**Admin Panel**: `http://localhost:9000/app`

### 3. Setup Frontend (SvelteKit)

```bash
cd frontend

# Install dependencies
yarn install

# Configure environment
cp .env.example .env.local
# Edit .env.local with backend API URL

# Start frontend server
yarn dev
```

**Frontend will run on**: `http://localhost:5173`

### 4. Access Applications

- **🛍️ Store Frontend**: http://localhost:5173
- **⚙️ Admin Panel**: http://localhost:9000/app  
- **🔌 API Endpoints**: http://localhost:9000

## 📁 Project Structure

```
hebrew-book-store/
├── 📂 backend/                 # MedusaJS Backend (Independent)
│   ├── 📂 src/
│   │   ├── 📂 api/            # API routes
│   │   ├── 📂 modules/        # Custom modules
│   │   ├── 📂 subscribers/    # Event subscribers
│   │   └── 📂 workflows/      # Business logic workflows
│   ├── 📄 medusa-config.ts    # MedusaJS configuration
│   ├── 📄 package.json        # Backend dependencies
│   └── 📄 .env                # Backend environment variables
│
├── 📂 frontend/                # SvelteKit Frontend (Independent)
│   ├── 📂 src/
│   │   ├── 📂 routes/         # SvelteKit pages
│   │   ├── 📂 lib/            # Shared utilities
│   │   └── 📂 components/     # Svelte components
│   ├── 📄 svelte.config.js    # SvelteKit configuration
│   ├── 📄 package.json        # Frontend dependencies
│   └── 📄 .env.local          # Frontend environment variables
│
├── 📂 docs/                    # Documentation
├── 📂 scripts/                 # Setup utilities
├── 📄 package.json             # Root scripts (no dependencies)
└── 📄 README.md               # This file
```

## 🔧 Development

### Backend Development

```bash
cd backend
yarn dev                    # Start with hot reload
yarn build                  # Build for production
yarn start                  # Start production server
yarn test                   # Run tests
```

### Frontend Development

```bash
cd frontend
yarn dev                    # Start with hot reload
yarn build                  # Build for production
yarn preview                # Preview production build
yarn test                   # Run tests
```

### Root Level Commands

```bash
# Start both applications
npm run dev:backend         # Start backend only
npm run dev:frontend        # Start frontend only

# Build both applications
npm run build:backend       # Build backend only
npm run build:frontend      # Build frontend only
npm run build              # Build both

# Test both applications
npm run test:backend        # Test backend only
npm run test:frontend       # Test frontend only
npm run test               # Test both
```

## 📚 Documentation

### Setup Guides
- [📋 Digital Products Setup](./DIGITAL_PRODUCTS_SETUP.md) - Configure digital book delivery
- [⚙️ Admin Configuration Guide](./ADMIN_CONFIGURATION_GUIDE.md) - Admin panel setup
- [👥 Customer Registration Guide](./CUSTOMER_REGISTRATION_GUIDE.md) - User management
- [💳 Checkout & Payment Setup](./CHECKOUT_PAYMENT_SETUP.md) - Payment integration

### Feature Guides  
- [📖 Library Configuration](./LIBRARY_CONFIGURATION.md) - Customer library setup
- [🧪 Testing Guide](./TESTING_GUIDE.md) - Testing procedures
- [🚀 Deployment Guide](./DEPLOYMENT_GUIDE.md) - Production deployment

### Quick References
- [⚡ Digital Products Quick Reference](./DIGITAL_PRODUCTS_QUICK_REFERENCE.md) - Copy-paste templates

## 🌐 API Integration

The frontend communicates with the backend via RESTful API calls:

```javascript
// Example API call from frontend
const response = await fetch('http://localhost:9000/store/products', {
  headers: {
    'x-publishable-api-key': 'your-api-key'
  }
})
```

## 🚀 Deployment

### Backend Deployment
- Deploy MedusaJS backend to any Node.js hosting service
- Configure PostgreSQL database
- Set environment variables

### Frontend Deployment  
- Deploy SvelteKit frontend to any static hosting service
- Configure API endpoint URL
- Set environment variables

### Recommended Hosting
- **Backend**: Railway, Heroku, DigitalOcean, AWS
- **Frontend**: Vercel, Netlify, Cloudflare Pages
- **Database**: Railway PostgreSQL, Heroku Postgres, AWS RDS

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

**🎯 This is a true headless e-commerce platform - backend and frontend are completely independent and can be developed, deployed, and scaled separately!**
