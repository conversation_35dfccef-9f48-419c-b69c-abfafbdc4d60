import { browser } from '$app/environment'
import { init, register, locale, waitLocale } from 'svelte-i18n'
import Cookies from 'js-cookie'

const defaultLocale = 'en'

// Register translations
register('ru', () => import('./locales/ru.json'))
register('he', () => import('./locales/he.json'))
register('en', () => import('./locales/en.json'))

// Function to determine language
function getInitialLocale(): string {
  if (browser) {
    // First check cookie
    const savedLocale = Cookies.get('locale')
    if (savedLocale && ['ru', 'he', 'en'].includes(savedLocale)) {
      return savedLocale
    }
    
    // Then check browser language
    const browserLocale = navigator.language.split('-')[0]
    if (['ru', 'he', 'en'].includes(browserLocale)) {
      return browserLocale
    }
  }
  
  return defaultLocale
}

// Initialize i18n
init({
  fallbackLocale: defaultLocale,
  initialLocale: getInitialLocale(),
})

// Function to change language
export function setLocale(newLocale: string) {
  if (['ru', 'he', 'en'].includes(newLocale)) {
    locale.set(newLocale)
    if (browser) {
      Cookies.set('locale', newLocale, { expires: 365 })
      // Update text direction for Hebrew
      document.documentElement.dir = newLocale === 'he' ? 'rtl' : 'ltr'
      document.documentElement.lang = newLocale
    }
  }
}

// Function to get current language
export function getCurrentLocale(): string {
  let currentLocale = defaultLocale
  locale.subscribe(value => {
    if (value) currentLocale = value
  })()
  return currentLocale
}

// Function to check RTL
export function isRTL(currentLocale?: string): boolean {
  const loc = currentLocale || getCurrentLocale()
  return loc === 'he'
}

// Export main functions
export { locale, waitLocale }
export { _ } from 'svelte-i18n'
