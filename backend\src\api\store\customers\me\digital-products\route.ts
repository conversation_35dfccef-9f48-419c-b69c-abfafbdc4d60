import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"
import {
  ContainerRegistrationKeys,
} from "@medusajs/framework/utils"

// List customer's purchased digital products
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

  try {
    console.log('🔍 Getting digital products for customer:', req.auth_context.actor_id)

    const { data: [customer] } = await query.graph({
      entity: "customer",
      fields: [
        "orders.*",
        "orders.items.*",
        "orders.items.product.*",
        "orders.items.variant.*",
        "orders.items.variant.digital_product.*",
        "orders.items.variant.digital_product.medias.*",
      ],
      filters: {
        id: req.auth_context.actor_id,
      },
    })

    if (!customer) {
      return res.status(404).json({
        error: "Customer not found",
      })
    }

    // Extract unique digital products from all orders using proper entity relationships
    const digitalProducts = {}
    customer.orders?.forEach((order: any) => {
      console.log(`📦 Processing order ${order.id} with ${order.items?.length || 0} items`)

      order.items?.forEach((item: any, itemIndex: number) => {
        console.log(`  📋 Item ${itemIndex + 1}:`)
        console.log('    - Product:', item.product?.title)
        console.log('    - Variant:', item.variant?.title)
        console.log('    - Has Digital Product:', !!item.variant?.digital_product)

        // Check if this variant has a linked digital product
        const digitalProduct = item.variant?.digital_product
        if (digitalProduct && digitalProduct.medias && digitalProduct.medias.length > 0) {
          console.log('    ✅ Found digital product:', digitalProduct.name)
          console.log('    📁 Digital medias:', digitalProduct.medias.length)

          // Log each media for debugging
          digitalProduct.medias.forEach((media: any, mediaIndex: number) => {
            console.log(`      Media ${mediaIndex + 1}: ${media.type} - ${media.url || media.fileId || 'No URL/File'}`)
          })

          // Use digital product ID as key to avoid duplicates
          digitalProducts[digitalProduct.id] = {
            id: digitalProduct.id,
            name: digitalProduct.name,
            description: item.product?.description || '',
            thumbnail: item.product?.thumbnail || null,
            medias: digitalProduct.medias,
            purchased_at: order.created_at,
            order_id: order.id,
            product_title: item.product?.title,
            variant_title: item.variant?.title,
          }
          console.log('    📚 Digital product added to customer library')
        } else {
          console.log('    ℹ️ No digital product linked to this variant')
        }
      })
    })

    const digitalProductsList = Object.values(digitalProducts)
    console.log('✅ CUSTOMER DIGITAL PRODUCTS - Successfully retrieved')
    console.log('  - Customer ID:', req.auth_context.actor_id)
    console.log('  - Orders processed:', customer.orders?.length || 0)
    console.log('  - Digital products found:', digitalProductsList.length)

    digitalProductsList.forEach((dp: any, index: number) => {
      console.log(`  Digital Product ${index + 1}:`)
      console.log('    - ID:', dp.id)
      console.log('    - Name:', dp.name)
      console.log('    - Medias:', dp.medias?.length || 0)
      console.log('    - Purchased:', dp.purchased_at)
    })

    console.log('=' .repeat(80))

    res.json({
      digital_products: digitalProductsList,
    })
  } catch (error: any) {
    console.error('❌ CUSTOMER DIGITAL PRODUCTS - Error retrieving products')
    console.error('  - Customer ID:', req.auth_context?.actor_id || 'Unknown')
    console.error('  - Error Type:', error.constructor.name)
    console.error('  - Error Message:', error.message)
    console.error('  - Stack Trace:', error.stack)
    console.error('=' .repeat(80))

    res.status(500).json({
      error: error.message,
      message: "Failed to retrieve digital products",
    })
  }
}
