# Alternative workflow - let Azure handle the build
name: Deploy to Azure (Azure builds)

on:
  workflow_dispatch:  # Manual trigger only for testing

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@v4
      
      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_3393D489ACA148AEA5AEBE7711560F91 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_F9EA7C3648F040778F81024500DA12B7 }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_BE9ED13D58B342E5A0D767CF12E0FB2C }}

      - name: 'Deploy to Azure Web App (Azure builds)'
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'hebrew-book'
          slot-name: 'Production'
          package: './backend'
          startup-command: 'npm install && npx medusa build && npx medusa migrations run && npx medusa start'
