FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Set environment variables
ENV NODE_ENV=production
ENV MEDUSA_ADMIN_ONBOARDING_TYPE=default

# Build the application (admin is built-in for v2.8.4)
RUN echo "=== Building MedusaJS v2.8.4 with built-in admin ===" && \
    yarn build && \
    echo "Build completed successfully"

# Verify the build and check for .medusa directory
RUN echo "=== Build verification ===" && \
    ls -la /app && \
    if [ -d "/app/.medusa" ]; then \
        echo "Found .medusa directory" && ls -la /app/.medusa/; \
        if [ -d "/app/.medusa/server" ]; then \
            echo "Found .medusa/server directory" && ls -la /app/.medusa/server/; \
        fi; \
    else \
        echo "No .medusa directory found - using root directory"; \
    fi

# Don't clean up dev dependencies yet - modules might need them
# RUN yarn install --frozen-lockfile --production && yarn cache clean

# Expose port
EXPOSE 9000

# Start the application with proper error handling
CMD ["sh", "-c", "if [ -d '.medusa/server' ]; then echo 'Starting from .medusa/server' && cd .medusa/server && yarn install --production && yarn start; else echo 'Starting from root directory' && yarn predeploy && yarn start; fi"]