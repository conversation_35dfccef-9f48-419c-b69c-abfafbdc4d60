# Hebrew Book Store - Frontend (SvelteKit)

This is the **independent frontend application** for the Hebrew Book Store e-commerce platform, built with SvelteKit.

## 🎨 Frontend Features

- **🌐 Multilingual Support**: Hebrew (RTL) and English (LTR) with automatic language detection
- **📱 Responsive Design**: Mobile-first approach with Tailwind CSS
- **🛍️ E-commerce UI**: Product catalog, cart, checkout, customer account
- **📖 Digital Library**: Customer library with FlipHTML5 book reader integration
- **💳 Payment Integration**: Stripe Checkout for secure payments
- **🔐 Authentication**: Customer registration, login, and session management
- **🎯 Modern Stack**: SvelteKit + TypeScript + Tailwind CSS

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+
- **Yarn** package manager
- **Backend API** running on http://localhost:9000

### Installation

```bash
# Install dependencies
yarn install

# Configure environment variables
cp .env.example .env.local
# Edit .env.local with backend API URL

# Start development server
yarn dev
```

### Access Points

- **🛍️ Store Frontend**: http://localhost:5173
- **📖 Customer Library**: http://localhost:5173/library
- **👤 Customer Account**: http://localhost:5173/account

## 📁 Project Structure

```
frontend/
├── 📂 src/
│   ├── 📂 routes/             # SvelteKit pages
│   │   ├── 📄 +layout.svelte  # Main layout
│   │   ├── 📄 +page.svelte    # Home page
│   │   ├── 📂 catalog/        # Product catalog
│   │   ├── 📂 cart/           # Shopping cart
│   │   ├── 📂 checkout/       # Checkout process
│   │   ├── 📂 library/        # Digital library
│   │   ├── 📂 account/        # Customer account
│   │   └── 📂 orders/         # Order history
│   ├── 📂 lib/                # Shared utilities
│   │   ├── 📂 api/            # API client
│   │   ├── 📂 components/     # Reusable components
│   │   ├── 📂 stores/         # Svelte stores
│   │   └── 📂 utils/          # Helper functions
│   └── 📂 static/             # Static assets
├── 📄 svelte.config.js        # SvelteKit configuration
├── 📄 tailwind.config.js      # Tailwind CSS configuration
├── 📄 package.json            # Dependencies
└── 📄 .env.local              # Environment variables
```

## 🔌 API Integration

The frontend communicates with the backend via RESTful API calls:

```javascript
// Example API usage
import { apiClient } from '$lib/api/client'

// Get products
const products = await apiClient.getProducts()

// Customer authentication
const customer = await apiClient.loginCustomer({
  email: '<EMAIL>',
  password: 'password'
})

// Add to cart
await apiClient.addToCart(cartId, {
  variant_id: 'variant_123',
  quantity: 1
})
```

## 🛠️ Development

### Available Scripts

```bash
yarn dev                    # Start development server
yarn build                  # Build for production
yarn preview                # Preview production build
yarn test                   # Run tests
yarn lint                   # Lint code
yarn format                 # Format code
```

### Environment Variables

Create a `.env.local` file with:

```env
# Backend API URL
VITE_API_BASE_URL=http://localhost:9000

# MedusaJS Publishable API Key
VITE_MEDUSA_PUBLISHABLE_KEY=pk_your_publishable_key_here

# Stripe Publishable Key (for payments)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...

# Default Region
VITE_DEFAULT_REGION_ID=reg_01...

# Default Sales Channel
VITE_DEFAULT_SALES_CHANNEL_ID=sc_01...
```

## 🌐 Multilingual Support

### Supported Languages
- **Hebrew** (עברית) - RTL layout
- **English** - LTR layout

### Language Features
- **Automatic Detection**: Based on browser settings
- **Manual Switching**: Language selector in header
- **RTL Support**: Proper right-to-left layout for Hebrew
- **Localized Content**: Translated UI text and messages

## 📖 Digital Library Integration

### FlipHTML5 Integration
The frontend includes a complete digital library system with embedded book reader and purchased books management.

### Features
- **Purchased Books**: Display customer's digital library
- **FlipHTML5 Reader**: Embedded book reader
- **Book Selection**: Easy switching between books
- **Responsive Design**: Works on mobile and desktop

## 🎨 Styling & Design

### Tailwind CSS
- **Utility-first** CSS framework
- **Responsive design** with mobile-first approach
- **RTL support** for Hebrew layout
- **Custom theme** with Hebrew book store branding

### Component Library
- **Reusable components** in `src/lib/components/`
- **Consistent styling** across the application
- **Accessible design** with proper ARIA labels
- **Loading states** and error handling

## 🚀 Deployment

### Production Build

```bash
# Build the application
yarn build

# Preview the build
yarn preview
```

### Recommended Hosting
- **Vercel** - Zero-config deployment with SvelteKit
- **Netlify** - Static site hosting with forms
- **Cloudflare Pages** - Fast global CDN
- **GitHub Pages** - Free static hosting

### Environment Setup
- Configure production API URL
- Set production API keys
- Update CORS settings in backend
- Configure domain and SSL

## 🧪 Testing

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run specific test file
yarn test src/lib/api/client.test.ts
```

### Testing Stack
- **Vitest** - Fast unit testing
- **Testing Library** - Component testing
- **Playwright** - E2E testing (optional)

## 📖 Documentation

- [SvelteKit Documentation](https://kit.svelte.dev/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Backend API Documentation](../backend/README.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**This frontend is completely independent and can be deployed separately from the backend!**
