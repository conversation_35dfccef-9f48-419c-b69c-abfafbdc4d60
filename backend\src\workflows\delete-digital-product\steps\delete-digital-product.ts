import { 
  createStep, 
  StepResponse,
} from "@medusajs/framework/workflows-sdk"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import { DIGITAL_PRODUCT_MODULE } from "../../../modules/digital-product"
import DigitalProductModuleService from "../../../modules/digital-product/service"

type DeleteDigitalProductStepInput = {
  variant_id: string
}

export const deleteDigitalProductStep = createStep(
  "delete-digital-product-step",
  async (input: DeleteDigitalProductStepInput, { container }) => {
    const digitalProductModuleService: DigitalProductModuleService =
      container.resolve(DIGITAL_PRODUCT_MODULE)

    const digitalProducts = await digitalProductModuleService.listDigitalProducts({
      product_variant_id: input.variant_id,
    })

    if (digitalProducts.length > 0) {
      await digitalProductModuleService.deleteDigitalProducts(
        digitalProducts.map((dp: any) => dp.id)
      )
    }

    return new StepResponse(digitalProducts)
  }
)
