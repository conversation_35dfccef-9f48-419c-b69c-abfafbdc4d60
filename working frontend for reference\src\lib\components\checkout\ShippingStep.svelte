<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
    import { formatPriceWithCurrency } from '$lib/stores/currency';

  export let cartId: string
  export let selectedMethod: any = null
  export let isLoading: boolean = false
  export let onSubmit: (method: any) => void
  export let onBack: () => void

  let shippingOptions: any[] = []
  let selectedOption: any = selectedMethod
  let loadingOptions = false
  let optionsError = ''

  onMount(() => {
    loadShippingOptions()
  })

  async function loadShippingOptions() {
    try {
      loadingOptions = true
      optionsError = ''

      const response = await apiClient.getShippingOptions(cartId)
      if (response.data?.shipping_options) {
        shippingOptions = response.data.shipping_options
        
        // Pre-select first option if none selected
        if (!selectedOption && shippingOptions.length > 0) {
          selectedOption = shippingOptions[0]
        }
      }
    } catch (error) {
      console.error('Failed to load shipping options:', error)
      optionsError = $_('checkout.failed_to_load_shipping')
    } finally {
      loadingOptions = false
    }
  }

  function handleSubmit() {
    if (!selectedOption) {
      return
    }

    onSubmit(selectedOption)
  }

  function selectOption(option: any) {
    selectedOption = option
  }
</script>

<div class="card-classic p-6">
  <div class="mb-6">
    <h2 class="font-book-title text-xl font-bold mb-2" style="color: var(--color-text-primary);">
      🚚 {$_('checkout.step_shipping')}
    </h2>
    <p class="font-book-text" style="color: var(--color-text-secondary);">
      {$_('checkout.shipping_description')}
    </p>
  </div>

  {#if loadingOptions}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
      <p class="font-book-text" style="color: var(--color-text-secondary);">
        {$_('checkout.loading_shipping_options')}
      </p>
    </div>
  {:else if optionsError}
    <div class="text-center py-8">
      <div class="text-4xl mb-4">❌</div>
      <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
        {$_('checkout.error_loading_shipping')}
      </h3>
      <p class="font-book-text text-sm mb-4" style="color: var(--color-text-secondary);">
        {optionsError}
      </p>
      <button on:click={loadShippingOptions} class="btn-classic-small">
        {$_('checkout.retry')}
      </button>
    </div>
  {:else if shippingOptions.length === 0}
    <div class="text-center py-8">
      <div class="text-4xl mb-4">📦</div>
      <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
        {$_('checkout.no_shipping_options')}
      </h3>
      <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
        {$_('checkout.no_shipping_options_description')}
      </p>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit} class="space-y-4">
      <!-- Shipping Options -->
      <div class="space-y-3">
        {#each shippingOptions as option (option.id)}
          <label class="block cursor-pointer">
            <div class="border-2 rounded-lg p-4 transition-all duration-200 hover:border-blue-300
              {selectedOption?.id === option.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:bg-gray-50'}"
            >
              <div class="flex items-center">
                <input
                  type="radio"
                  name="shipping_option"
                  value={option.id}
                  checked={selectedOption?.id === option.id}
                  on:change={() => selectOption(option)}
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div class="ml-3 flex-1">
                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-book-title text-sm font-semibold" style="color: var(--color-text-primary);">
                        {option.name}
                      </h4>
                      {#if option.data?.description}
                        <p class="font-book-text text-xs mt-1" style="color: var(--color-text-secondary);">
                          {option.data.description}
                        </p>
                      {/if}
                    </div>
                    <div class="text-right">
                      <p class="font-book-title text-sm font-semibold" style="color: var(--color-text-primary);">
                        {#if option.amount}
                          {formatPriceWithCurrency(option.amount)} 
                        {:else}
                          {$_('checkout.free')}
                        {/if}
                      </p>
                      {#if option.data?.estimated_delivery}
                        <p class="font-book-text text-xs" style="color: var(--color-text-secondary);">
                          {option.data.estimated_delivery}
                        </p>
                      {/if}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </label>
        {/each}
      </div>

      <!-- Form Actions -->
      <div class="flex justify-between pt-6">
        <button
          type="button"
          on:click={onBack}
          disabled={isLoading}
          class="btn-classic-outline"
          class:opacity-50={isLoading}
        >
          ← {$_('checkout.back')}
        </button>

        <button
          type="submit"
          disabled={isLoading || !selectedOption}
          class="btn-classic"
          class:opacity-50={isLoading || !selectedOption}
        >
          {#if isLoading}
            <div class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {$_('checkout.processing')}
            </div>
          {:else}
            {$_('checkout.continue')} →
          {/if}
        </button>
      </div>
    </form>
  {/if}
</div>
