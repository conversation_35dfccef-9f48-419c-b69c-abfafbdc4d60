# 🛒 Product API Requests Guide

## 📋 Overview

This guide shows how to fetch products from the Hebrew Book Store using MedusaJS v2 API endpoints.

## 🔧 Base Configuration

### API Base URL
```
Backend: http://localhost:9000
Admin API: http://localhost:9000/admin
Store API: http://localhost:9000/store
```

### Authentication
```javascript
// Admin API requires authentication
const adminHeaders = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer your_admin_token'
}

// Store API uses publishable key
const storeHeaders = {
  'Content-Type': 'application/json',
  'x-publishable-api-key': 'pk_your_publishable_key'
}
```

## 🛍️ Store API Requests (Frontend)

### 1. Fetch All Products with Variants and Pricing

```javascript
// GET /store/products
const fetchProducts = async (regionId) => {
  const response = await fetch(
    `http://localhost:9000/store/products?region_id=${regionId}&fields=*variants.calculated_price`,
    {
      headers: {
        'x-publishable-api-key': 'pk_your_key_here'
      }
    }
  )
  
  const data = await response.json()
  return data.products
}

// Example response structure:
{
  "products": [
    {
      "id": "prod_01ABC123",
      "title": "Hebrew Grammar Basics",
      "description": "Learn Hebrew grammar fundamentals",
      "handle": "hebrew-grammar-basics",
      "status": "published",
      "thumbnail": "https://example.com/image.jpg",
      "metadata": {
        "chapters_collection_id": "pcol_01XYZ789",
        "is_digital_product": "true"
      },
      "variants": [
        {
          "id": "variant_01DEF456",
          "title": "Default Variant",
          "sku": "HGB-001",
          "calculated_price": {
            "calculated_amount": 2999,
            "currency_code": "ILS"
          }
        }
      ]
    }
  ]
}
```

### 2. Fetch Products by Type (Books Only)

```javascript
// Filter by product type
const fetchBookProducts = async (regionId) => {
  const response = await fetch(
    `http://localhost:9000/store/products?region_id=${regionId}&type_id=ptyp_book&fields=*variants.calculated_price`,
    {
      headers: {
        'x-publishable-api-key': 'pk_your_key_here'
      }
    }
  )
  
  return response.json()
}
```

### 3. Fetch Products with Metadata

```javascript
// Get products with specific metadata
const fetchProductsWithMetadata = async (productIds) => {
  const params = new URLSearchParams()
  productIds.forEach(id => params.append('id', id))
  params.append('fields', 'id,metadata')
  
  const response = await fetch(
    `http://localhost:9000/store/products?${params}`,
    {
      headers: {
        'x-publishable-api-key': 'pk_your_key_here'
      }
    }
  )
  
  return response.json()
}
```

### 4. Fetch Products by Collection (Chapters)

```javascript
// Get products in a specific collection
const fetchProductsByCollection = async (collectionId, regionId) => {
  const response = await fetch(
    `http://localhost:9000/store/products?region_id=${regionId}&collection_id=${collectionId}&fields=*variants.calculated_price`,
    {
      headers: {
        'x-publishable-api-key': 'pk_your_key_here'
      }
    }
  )
  
  return response.json()
}
```

### 5. Search Products

```javascript
// Search products by title/description
const searchProducts = async (query, regionId) => {
  const response = await fetch(
    `http://localhost:9000/store/products?q=${encodeURIComponent(query)}&region_id=${regionId}&fields=*variants.calculated_price`,
    {
      headers: {
        'x-publishable-api-key': 'pk_your_key_here'
      }
    }
  )
  
  return response.json()
}
```

## ⚙️ Admin API Requests (Backend Management)

### 1. Fetch All Products (Admin)

```javascript
// GET /admin/products
const fetchAdminProducts = async () => {
  const response = await fetch(
    'http://localhost:9000/admin/products?fields=*categories,*shipping_profile,-variants',
    {
      credentials: 'include', // For session-based auth
      headers: {
        'Content-Type': 'application/json'
      }
    }
  )
  
  return response.json()
}
```

### 2. Fetch Product with Variants (Admin)

```javascript
// Get specific product with all variants
const fetchProductWithVariants = async (productId) => {
  const response = await fetch(
    `http://localhost:9000/admin/products/${productId}/variants?fields=title,sku,*options,*inventory_items.inventory.location_levels,inventory_quantity,manage_inventory`,
    {
      credentials: 'include'
    }
  )
  
  return response.json()
}
```

### 3. Fetch Product Variants (Admin)

```javascript
// Get all product variants with product info
const fetchProductVariants = async () => {
  const response = await fetch(
    'http://localhost:9000/admin/product-variants?fields=id,title,sku,product_id,*product&limit=100',
    {
      credentials: 'include'
    }
  )
  
  return response.json()
}

// Example response:
{
  "variants": [
    {
      "id": "variant_01ABC123",
      "title": "Default Variant",
      "sku": "HGB-001",
      "product_id": "prod_01XYZ789",
      "product": {
        "id": "prod_01XYZ789",
        "title": "Hebrew Grammar Basics",
        "handle": "hebrew-grammar-basics"
      }
    }
  ]
}
```

## 🔗 Digital Products API

### 1. Fetch Digital Products

```javascript
// GET /admin/digital-products
const fetchDigitalProducts = async () => {
  const response = await fetch(
    'http://localhost:9000/admin/digital-products?fields=*,*medias,*product_variant&limit=100',
    {
      credentials: 'include'
    }
  )
  
  return response.json()
}

// Example response:
{
  "digital_products": [
    {
      "id": "dp_01ABC123",
      "name": "Hebrew Grammar Digital Book",
      "medias": [
        {
          "id": "dpm_01DEF456",
          "type": "main",
          "url": "https://online.fliphtml5.com/xrcwd/qlyk/",
          "mimeType": "text/html"
        }
      ],
      "product_variant": {
        "id": "variant_01GHI789",
        "product_id": "prod_01JKL012"
      }
    }
  ]
}
```

### 2. Create Digital Product

```javascript
// POST /admin/digital-products
const createDigitalProduct = async (digitalProductData) => {
  const response = await fetch(
    'http://localhost:9000/admin/digital-products',
    {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: digitalProductData.name,
        product_variant_id: digitalProductData.product_variant_id,
        medias: [
          {
            type: 'main',
            url: digitalProductData.fliphtml5_url,
            useDirectUrl: true
          }
        ]
      })
    }
  )
  
  return response.json()
}
```

## 🎯 Practical Examples

### Frontend Product Display

```javascript
// Complete product fetching for main page
const loadMainPageProducts = async () => {
  try {
    // 1. Get region for pricing
    const regionsResponse = await fetch('/store/regions', {
      headers: { 'x-publishable-api-key': process.env.VITE_MEDUSA_PUBLISHABLE_API_KEY }
    })
    const { regions } = await regionsResponse.json()
    const defaultRegion = regions[0]
    
    // 2. Fetch products with pricing
    const productsResponse = await fetch(
      `/store/products?region_id=${defaultRegion.id}&limit=100&fields=*variants.calculated_price`,
      {
        headers: { 'x-publishable-api-key': process.env.VITE_MEDUSA_PUBLISHABLE_API_KEY }
      }
    )
    const { products } = await productsResponse.json()
    
    // 3. Get metadata for products
    const productIds = products.map(p => p.id)
    const metadataResponse = await fetch(
      `/store/products?${productIds.map(id => `id=${id}`).join('&')}&fields=id,metadata`,
      {
        headers: { 'x-publishable-api-key': process.env.VITE_MEDUSA_PUBLISHABLE_API_KEY }
      }
    )
    const { products: productsWithMetadata } = await metadataResponse.json()
    
    // 4. Merge data
    return products.map(product => ({
      ...product,
      metadata: productsWithMetadata.find(p => p.id === product.id)?.metadata || {}
    }))
    
  } catch (error) {
    console.error('Error loading products:', error)
    return []
  }
}
```

### Admin Product Management

```javascript
// Admin: Get product with digital product info
const getProductWithDigitalInfo = async (productId) => {
  try {
    // 1. Get product details
    const productResponse = await fetch(
      `/admin/products/${productId}?fields=*categories,*shipping_profile,-variants`,
      { credentials: 'include' }
    )
    const { product } = await productResponse.json()
    
    // 2. Get product variants
    const variantsResponse = await fetch(
      `/admin/products/${productId}/variants`,
      { credentials: 'include' }
    )
    const { variants } = await variantsResponse.json()
    
    // 3. Check for linked digital products
    const digitalProductsResponse = await fetch(
      '/admin/digital-products?fields=*,*medias,*product_variant&limit=100',
      { credentials: 'include' }
    )
    const { digital_products } = await digitalProductsResponse.json()
    
    // 4. Find linked digital product
    const variantIds = variants.map(v => v.id)
    const linkedDigitalProduct = digital_products.find(dp => 
      dp.product_variant && variantIds.includes(dp.product_variant.id)
    )
    
    return {
      product,
      variants,
      digitalProduct: linkedDigitalProduct
    }
    
  } catch (error) {
    console.error('Error fetching product info:', error)
    return null
  }
}
```

## 🔍 Query Parameters Reference

### Common Store API Parameters
- `region_id`: Required for pricing calculations
- `fields`: Specify which fields to include/exclude
- `limit`: Number of results (default: 20, max: 100)
- `offset`: Pagination offset
- `q`: Search query
- `collection_id`: Filter by collection
- `type_id`: Filter by product type
- `id`: Filter by specific product IDs (multiple allowed)

### Common Admin API Parameters
- `fields`: Field selection (supports nested fields with *)
- `limit`: Results limit
- `offset`: Pagination
- `q`: Search query
- `order`: Sort order

## 🚀 Performance Tips

1. **Use field selection** to reduce payload size
2. **Implement pagination** for large datasets
3. **Cache frequently accessed data** (regions, product types)
4. **Use batch requests** for metadata fetching
5. **Implement proper error handling** for network failures

This guide covers the essential product fetching patterns for the Hebrew Book Store. Adjust the examples based on your specific requirements and authentication setup.
