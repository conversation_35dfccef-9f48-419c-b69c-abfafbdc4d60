<script lang="ts">
  import { onMount } from 'svelte'
  import { page } from '$app/stores'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { formatPriceWithCurrency } from '$lib/stores/currency'

  let order: any = null
  let loading = true
  let error = ''

  onMount(async () => {
    await loadOrder()
  })

  async function loadOrder() {
    try {
      const orderId = $page.params.orderId
      if (!orderId) {
        throw new Error('No order ID provided')
      }

      console.log('🔄 Loading order:', orderId)

      // Get auth token
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('Not authenticated')
      }

      // Load order details
      const response = await apiClient.getOrder(orderId, token)
      if (response.data?.order) {
        order = response.data.order
        console.log('✅ Order loaded:', order)
      } else {
        throw new Error('Order not found')
      }
    } catch (err: any) {
      console.error('❌ Failed to load order:', err)
      error = err.message || 'Failed to load order'

      // If order not found, redirect to success page after a delay
      setTimeout(() => {
        window.location.href = '/checkout/success'
      }, 3000)
    } finally {
      loading = false
    }
  }
</script>

<svelte:head>
  <title>{$_('orders.confirmation_title')} - Hebrew Book Store</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    {#if loading}
      <!-- Loading State -->
      <div class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-gray-600">{$_('orders.loading_order')}</p>
      </div>
    {:else if error}
      <!-- Error State -->
      <div class="text-center py-12">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h2 class="text-xl font-bold text-red-800 mb-2">{$_('orders.error_title')}</h2>
          <p class="text-red-600 mb-4">{error}</p>
          <a href="/" class="btn-classic">
            {$_('common.back_to_home')}
          </a>
        </div>
      </div>
    {:else if order}
      <!-- Order Confirmation -->
      <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <!-- Header -->
        <div class="bg-green-50 border-b border-green-200 px-6 py-8 text-center">
          <div class="text-green-500 text-6xl mb-4">✅</div>
          <h1 class="text-3xl font-bold text-green-800 mb-2">
            {$_('orders.order_confirmed')}
          </h1>
          <p class="text-green-700">
            {$_('orders.confirmation_message')}
          </p>
        </div>

        <!-- Order Details -->
        <div class="px-6 py-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <!-- Order Info -->
            <div>
              <h3 class="text-lg font-semibold mb-4">{$_('orders.order_details')}</h3>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">{$_('orders.order_number')}:</span>
                  <span class="font-mono">{order.display_id || order.id}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">{$_('orders.order_date')}:</span>
                  <span>{new Date(order.created_at).toLocaleDateString()}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">{$_('orders.status')}:</span>
                  <span class="capitalize">{order.status}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">{$_('orders.payment_status')}:</span>
                  <span class="capitalize">{order.payment_status}</span>
                </div>
              </div>
            </div>

            <!-- Customer Info -->
            <div>
              <h3 class="text-lg font-semibold mb-4">{$_('orders.customer_details')}</h3>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">{$_('orders.email')}:</span>
                  <span>{order.email}</span>
                </div>
                {#if order.billing_address}
                  <div class="flex justify-between">
                    <span class="text-gray-600">{$_('orders.billing_address')}:</span>
                    <div class="text-right">
                      <div>{order.billing_address.first_name} {order.billing_address.last_name}</div>
                      <div>{order.billing_address.address_1}</div>
                      {#if order.billing_address.address_2}
                        <div>{order.billing_address.address_2}</div>
                      {/if}
                      <div>{order.billing_address.city}, {order.billing_address.postal_code}</div>
                    </div>
                  </div>
                {/if}
              </div>
            </div>
          </div>

          <!-- Order Items -->
          <div class="mb-8">
            <h3 class="text-lg font-semibold mb-4">{$_('orders.items_purchased')}</h3>
            <div class="border border-gray-200 rounded-lg overflow-hidden">
              {#each order.items || [] as item}
                <div class="flex items-center justify-between p-4 border-b border-gray-100 last:border-b-0">
                  <div class="flex-1">
                    <h4 class="font-medium">{item.product_title || item.title}</h4>
                    {#if item.product_description}
                      <p class="text-sm text-gray-600 mt-1">{item.product_description}</p>
                    {/if}
                    <p class="text-sm text-gray-500 mt-1">
                      {$_('orders.quantity')}: {item.quantity}
                    </p>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">
                      {formatPriceWithCurrency(item.unit_price * item.quantity)}
                    </div>
                    <div class="text-sm text-gray-500">
                      {formatPriceWithCurrency(item.unit_price)} {$_('orders.each')}
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          </div>

          <!-- Order Total -->
          <div class="border-t border-gray-200 pt-6">
            <div class="flex justify-between items-center text-lg font-semibold">
              <span>{$_('orders.total')}:</span>
              <span>{formatPriceWithCurrency(order.total)}</span>
            </div>
          </div>

          <!-- Actions -->
          <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/library" class="btn-classic">
              📚 {$_('orders.view_library')}
            </a>
            <a href="/orders" class="btn-classic-outline">
              📋 {$_('orders.view_all_orders')}
            </a>
            <a href="/" class="btn-classic-outline">
              🏠 {$_('common.back_to_home')}
            </a>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .btn-classic {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors;
  }

  .btn-classic-outline {
    @apply inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors;
  }
</style>
