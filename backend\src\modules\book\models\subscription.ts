import { model } from "@medusajs/framework/utils"

const Subscription = model.define("subscription", {
  id: model.id().primaryKey(),
  user_id: model.text(),
  type: model.text(), // "6_months" or "12_months"
  price: model.number(), // Price paid for subscription
  starts_at: model.dateTime(),
  expires_at: model.dateTime(),
  is_active: model.boolean().default(true),
  auto_renew: model.boolean().default(false),
  payment_method_id: model.text().nullable(), // Stripe payment method ID
  stripe_subscription_id: model.text().nullable(), // Stripe subscription ID
  cancelled_at: model.dateTime().nullable(),
})

export default Subscription
