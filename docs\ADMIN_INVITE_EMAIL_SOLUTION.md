# 📧 Admin Invite Email Solution

## 🚨 Problem Identified

The original subscriber approach didn't work because:
1. **Medusa CLI doesn't emit the expected events** with email/token data
2. **Event timing issues** - subscriber runs before the invite data is available
3. **Missing event data** - the `invite.created` event doesn't contain the token

## ✅ Solutions Implemented

### **Solution 1: Manual API Endpoint**

**Endpoint**: `POST /admin/invites`

**Usage**:
```bash
# After creating invite with CLI
medusa user --email <EMAIL> --invite

# Copy the token from output, then call:
curl -X POST http://localhost:9000/admin/invites \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "token": "YOUR_INVITE_TOKEN"
  }'
```

### **Solution 2: Node.js Script**

**File**: `backend/scripts/create-admin-invite.js`

**Usage**:
```bash
cd backend
node scripts/create-admin-invite.js <EMAIL>
```

**What it does**:
1. ✅ Creates invite using Medusa CLI
2. ✅ Extracts token from CLI output
3. ✅ Sends email automatically
4. ✅ Shows invite URL

### **Solution 3: Manual Process**

**Step-by-step**:
1. **Create invite**:
   ```bash
   cd backend
   medusa user --email <EMAIL> --invite
   ```

2. **Copy the token** from CLI output

3. **Send email via API**:
   ```bash
   curl -X POST http://localhost:9000/admin/invites \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>", 
       "token": "PASTE_TOKEN_HERE"
     }'
   ```

## 🎯 Recommended Approach

**Use the Node.js Script** (`Solution 2`) because:
- ✅ **Fully automated** - one command does everything
- ✅ **Error handling** - shows clear error messages
- ✅ **Token extraction** - automatically parses CLI output
- ✅ **Email sending** - uses your Mailjet configuration
- ✅ **URL display** - shows the final invite link

## 🔧 How to Use

### **Method 1: Automated Script (Recommended)**

```bash
cd backend
node scripts/create-admin-invite.js <EMAIL>
```

**Expected Output**:
```
🔄 Creating admin invite for: <EMAIL>
📝 Creating invite via Medusa CLI...
✅ Invite token extracted: eyJhbGciOiJIUzI1NiIs...
📧 Sending invite email...
✅ Admin invite email sent successfully to: <EMAIL>

🎉 Admin invite created successfully!
📧 Email sent to: <EMAIL>
🔗 Invite link: http://localhost:9000/app/invite?token=...
⏰ Token expires in 7 days
```

### **Method 2: Manual API Call**

1. **Create invite**:
   ```bash
   medusa user --email <EMAIL> --invite
   ```

2. **Copy token** from output

3. **Send email**:
   ```bash
   curl -X POST http://localhost:9000/admin/invites \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "token": "YOUR_COPIED_TOKEN"
     }'
   ```

## 🐛 Troubleshooting

### **Script Errors**

**Error**: `medusa: command not found`
```bash
# Install Medusa CLI globally
npm install -g @medusajs/cli
```

**Error**: `Cannot find module`
```bash
# Install dependencies
cd backend
npm install
```

### **Email Errors**

**Error**: `Email sending failed`
- ✅ Check Mailjet API keys in `.env`
- ✅ Verify sender email is verified in Mailjet
- ✅ Check backend logs for detailed error

### **API Errors**

**Error**: `401 Unauthorized`
- ✅ Make sure you're authenticated as admin
- ✅ Use proper admin token in Authorization header

## 🚀 Future Improvements

1. **Admin Panel Integration** - Add invite creation to admin UI
2. **Automatic Event Handling** - Hook into Medusa's internal invite workflow
3. **Bulk Invites** - Support multiple email addresses
4. **Invite Management** - List, resend, and revoke invites

## 📝 Notes

- **Mailjet Configuration**: Make sure your Mailjet API keys are set in `.env`
- **Admin URL**: Verify `ADMIN_URL` points to your admin panel
- **Token Expiry**: Invite tokens expire in 7 days by default
- **Email Templates**: Customize the Hebrew email template in `NotificationService`

---

**The subscriber approach has been kept for future use when Medusa events are properly implemented.**
