<script lang="ts">
  import { _ } from '$lib/i18n'

  export let email: string = ''
  export let isLoading: boolean = false
  export let onSubmit: (email: string) => void

  let emailInput = email
  let emailError = ''

  function validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  function handleSubmit() {
    emailError = ''
    
    if (!emailInput.trim()) {
      emailError = $_('checkout.email_required')
      return
    }

    if (!validateEmail(emailInput.trim())) {
      emailError = $_('checkout.email_invalid')
      return
    }

    onSubmit(emailInput.trim())
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      handleSubmit()
    }
  }
</script>

<div class="card-classic p-6">
  <div class="mb-6">
    <h2 class="font-book-title text-xl font-bold mb-2" style="color: var(--color-text-primary);">
      📧 {$_('checkout.step_email')}
    </h2>
    <p class="font-book-text" style="color: var(--color-text-secondary);">
      {$_('checkout.email_description')}
    </p>
  </div>

  <form on:submit|preventDefault={handleSubmit} class="space-y-4">
    <div>
      <label for="email" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
        {$_('checkout.email_address')} *
      </label>
      <input
        id="email"
        type="email"
        bind:value={emailInput}
        on:keypress={handleKeyPress}
        disabled={isLoading}
        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
        class:border-red-500={emailError}
        class:opacity-50={isLoading}
        placeholder={$_('checkout.email_placeholder')}
        required
      />
      {#if emailError}
        <p class="mt-1 text-sm text-red-600 font-book-text">{emailError}</p>
      {/if}
    </div>

    <div class="flex justify-end pt-4">
      <button
        type="submit"
        disabled={isLoading || !emailInput.trim()}
        class="btn-classic"
        class:opacity-50={isLoading || !emailInput.trim()}
      >
        {#if isLoading}
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {$_('checkout.processing')}
          </div>
        {:else}
          {$_('checkout.continue')} →
        {/if}
      </button>
    </div>
  </form>
</div>
