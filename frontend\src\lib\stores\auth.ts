import { writable } from 'svelte/store'
import { apiClient } from '$lib/api/client'

export interface Customer {
  id: string
  email: string
  first_name?: string
  last_name?: string
  phone?: string
  created_at: string
  updated_at: string
}

export interface AuthState {
  isAuthenticated: boolean
  customer: Customer | null
  token: string | null
  isLoading: boolean
  error: string | null
}

const initialState: AuthState = {
  isAuthenticated: false,
  customer: null,
  token: null,
  isLoading: false,
  error: null
}

// Create the auth store
function createAuthStore() {
  const { subscribe, set, update } = writable<AuthState>(initialState)

  return {
    subscribe,

    // Register a new customer
    register: async (data: {
      email: string
      password: string
      first_name?: string
      last_name?: string
      phone?: string
    }) => {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        console.log('🔐 Starting customer registration for:', data.email)

        // Step 1: Try to get registration token
        let token: string
        try {
          const registerResponse = await apiClient.registerCustomer({
            email: data.email,
            password: data.password
          })

          if (registerResponse.error) {
            throw new Error(registerResponse.error)
          }

          token = registerResponse.data.token
          console.log('✅ Registration token obtained')
        } catch (registerError) {
          console.log('⚠️ Registration failed, trying login (existing user):', registerError)
          
          // Step 2: If registration fails, try login (user might already exist)
          try {
            const loginResponse = await apiClient.loginCustomer({
              email: data.email,
              password: data.password
            })

            if (loginResponse.error) {
              throw new Error(loginResponse.error)
            }

            token = loginResponse.data.token
            console.log('✅ Login token obtained for existing user')
          } catch (loginError) {
            throw new Error(`Registration failed: ${registerError.message}. Login also failed: ${loginError.message}`)
          }
        }

        // Step 3: Create customer profile
        const customerData = {
          email: data.email,
          first_name: data.first_name,
          last_name: data.last_name,
          phone: data.phone
        }

        const customerResponse = await apiClient.createCustomer(customerData, token)

        if (customerResponse.error) {
          throw new Error(customerResponse.error)
        }

        // Step 4: After successful customer creation, login to get proper auth token
        console.log('🔄 Logging in after registration to get proper auth token')

        const loginResponse = await apiClient.loginCustomer({
          email: data.email,
          password: data.password
        })

        if (loginResponse.error) {
          throw new Error(`Registration succeeded but login failed: ${loginResponse.error}`)
        }

        const authToken = loginResponse.data.token
        const customer = customerResponse.data.customer

        // Save to localStorage
        localStorage.setItem('auth_token', authToken)
        localStorage.setItem('customer', JSON.stringify(customer))

        update(state => ({
          ...state,
          isAuthenticated: true,
          customer,
          token: authToken,
          isLoading: false,
          error: null
        }))

        console.log('🎉 Customer registration and login completed successfully')

        // Create cart for new customer and handle any guest cart
        try {
          const { cartActions } = await import('$lib/stores/cart')
          await cartActions.handleLogin(customer.id)
        } catch (error) {
          console.warn('⚠️ Failed to handle cart after registration:', error)
        }

        return { success: true, customer }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Registration failed'
        console.error('❌ Registration error:', errorMessage)
        
        update(state => ({
          ...state,
          isLoading: false,
          error: errorMessage
        }))
        
        return { success: false, error: errorMessage }
      }
    },

    // Login existing customer
    login: async (data: {
      email: string
      password: string
    }) => {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        console.log('🔐 Starting customer login for:', data.email)

        // Step 1: Get login token
        const loginResponse = await apiClient.loginCustomer(data)

        if (loginResponse.error) {
          throw new Error(loginResponse.error)
        }

        const token = loginResponse.data.token

        // Step 2: Get customer profile
        const customerResponse = await apiClient.getCustomer(token)

        if (customerResponse.error) {
          throw new Error(customerResponse.error)
        }

        const customer = customerResponse.data.customer

        // Step 3: Store authentication state
        localStorage.setItem('auth_token', token)
        localStorage.setItem('customer', JSON.stringify(customer))

        update(state => ({
          ...state,
          isAuthenticated: true,
          customer,
          token,
          isLoading: false,
          error: null
        }))

        console.log('🎉 Customer login completed successfully')

        // Handle cart after successful login
        try {
          const { cartActions } = await import('$lib/stores/cart')
          await cartActions.handleLogin(customer.id)
        } catch (error) {
          console.warn('⚠️ Failed to handle cart after login:', error)
        }

        return { success: true, customer }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Login failed'
        console.error('❌ Login error:', errorMessage)
        
        update(state => ({
          ...state,
          isLoading: false,
          error: errorMessage
        }))
        
        return { success: false, error: errorMessage }
      }
    },

    // Logout customer
    logout: async () => {
      update(state => ({ ...state, isLoading: true }))

      try {
        const token = localStorage.getItem('auth_token')
        if (token) {
          await apiClient.logoutCustomer(token)
        }
      } catch (error) {
        console.warn('Logout API call failed:', error)
      }

      // Clear local storage and state regardless of API call result
      localStorage.removeItem('auth_token')
      localStorage.removeItem('customer')

      // Handle cart logout - convert backend cart to local
      try {
        const { cartActions } = await import('$lib/stores/cart')
        cartActions.handleLogout()
      } catch (error) {
        console.warn('⚠️ Failed to handle cart logout:', error)
      }

      set(initialState)
      console.log('👋 Customer logged out')
    },

    // Initialize auth state from localStorage with token validation
    initialize: async () => {
      try {
        const token = localStorage.getItem('auth_token')
        const customerData = localStorage.getItem('customer')

        if (token && customerData) {
          console.log('🔄 Found stored auth data, validating token...')

          // Validate token by making an authenticated request
          try {
            const customerResponse = await apiClient.getCustomer(token)

            if (customerResponse.error) {
              console.log('❌ Stored token is invalid:', customerResponse.error)
              // Clear invalid auth data
              localStorage.removeItem('auth_token')
              localStorage.removeItem('customer')
              set(initialState)
              return
            }

            // Token is valid, restore auth state with fresh customer data
            const customer = customerResponse.data.customer
            update(state => ({
              ...state,
              isAuthenticated: true,
              customer,
              token
            }))

            // Update localStorage with fresh customer data
            localStorage.setItem('customer', JSON.stringify(customer))
            console.log('✅ Auth state restored and validated from localStorage')

          } catch (error) {
            console.log('❌ Token validation failed:', error)
            // Clear invalid auth data
            localStorage.removeItem('auth_token')
            localStorage.removeItem('customer')
            set(initialState)
          }
        } else {
          console.log('ℹ️ No stored auth data found')
        }
      } catch (error) {
        console.error('Failed to initialize auth state:', error)
        // Clear corrupted data
        localStorage.removeItem('auth_token')
        localStorage.removeItem('customer')
        set(initialState)
      }
    },

    // Update customer information
    updateCustomer: (updatedCustomer: Customer) => {
      update(state => {
        const newState = {
          ...state,
          customer: updatedCustomer
        }

        // Update localStorage
        localStorage.setItem('customer', JSON.stringify(updatedCustomer))

        return newState
      })
      console.log('👤 Customer information updated')
    },

    // Clear error
    clearError: () => {
      update(state => ({ ...state, error: null }))
    },

    // Handle 401 unauthorized errors
    handleUnauthorized: () => {
      console.log('🚨 Handling 401 unauthorized - clearing auth state')

      // Clear local storage and state
      localStorage.removeItem('auth_token')
      localStorage.removeItem('customer')

      // Handle cart logout
      try {
        import('$lib/stores/cart').then(({ cartActions }) => {
          cartActions.handleLogout()
        })
      } catch (error) {
        console.warn('⚠️ Failed to handle cart logout:', error)
      }

      set(initialState)
      console.log('👋 Customer logged out due to invalid token')
    }
  }
}

export const authStore = createAuthStore()

// Derived stores for convenience
export const isAuthenticated = writable(false)
export const currentCustomer = writable<Customer | null>(null)

// Update derived stores when auth state changes
authStore.subscribe(state => {
  isAuthenticated.set(state.isAuthenticated)
  currentCustomer.set(state.customer)
})
