<script lang="ts">
  import { _ } from '$lib/i18n'

  export let billing_address: any = {}
  export let isLoading: boolean = false
  export let onSubmit: (address: any) => void
  export let onBack: () => void

  let billingForm = { ...billing_address }
  let errors: Record<string, string> = {}

  function validateForm(): boolean {
    errors = {}
    
    // Validate billing address
    if (!billingForm.first_name?.trim()) {
      errors.billing_first_name = $_('checkout.first_name_required')
    }
    if (!billingForm.last_name?.trim()) {
      errors.billing_last_name = $_('checkout.last_name_required')
    }
    if (!billingForm.address_1?.trim()) {
      errors.billing_address_1 = $_('checkout.address_required')
    }
    if (!billingForm.city?.trim()) {
      errors.billing_city = $_('checkout.city_required')
    }
    if (!billingForm.postal_code?.trim()) {
      errors.billing_postal_code = $_('checkout.postal_code_required')
    }

    return Object.keys(errors).length === 0
  }

  function handleSubmit() {
    if (!validateForm()) {
      return
    }

    onSubmit(billingForm)
  }
</script>

<div class="card-classic p-6">
  <div class="mb-6">
    <h2 class="font-book-title text-xl font-bold mb-2" style="color: var(--color-text-primary);">
      📍 {$_('checkout.step_billing')}
    </h2>
    <p class="font-book-text" style="color: var(--color-text-secondary);">
      {$_('checkout.billing_description')}
    </p>
  </div>

  <form on:submit|preventDefault={handleSubmit} class="space-y-6">
    <!-- Billing Address -->
    <div>
      <h3 class="font-book-title text-lg font-semibold mb-4" style="color: var(--color-text-primary);">
        {$_('checkout.billing_address')}
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="billing_first_name" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.first_name')} *
          </label>
          <input
            id="billing_first_name"
            type="text"
            bind:value={billingForm.first_name}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.billing_first_name}
            placeholder={$_('checkout.first_name_placeholder')}
            required
          />
          {#if errors.billing_first_name}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.billing_first_name}</p>
          {/if}
        </div>

        <div>
          <label for="billing_last_name" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.last_name')} *
          </label>
          <input
            id="billing_last_name"
            type="text"
            bind:value={billingForm.last_name}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.billing_last_name}
            placeholder={$_('checkout.last_name_placeholder')}
            required
          />
          {#if errors.billing_last_name}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.billing_last_name}</p>
          {/if}
        </div>
      </div>

      <div class="mt-4">
        <label for="billing_address_1" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.address_line_1')} *
        </label>
        <input
          id="billing_address_1"
          type="text"
          bind:value={billingForm.address_1}
          disabled={isLoading}
          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          class:border-red-500={errors.billing_address_1}
          placeholder={$_('checkout.address_line_1_placeholder')}
          required
        />
        {#if errors.billing_address_1}
          <p class="mt-1 text-sm text-red-600 font-book-text">{errors.billing_address_1}</p>
        {/if}
      </div>

      <div class="mt-4">
        <label for="billing_address_2" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.address_line_2')}
        </label>
        <input
          id="billing_address_2"
          type="text"
          bind:value={billingForm.address_2}
          disabled={isLoading}
          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          placeholder={$_('checkout.address_line_2_placeholder')}
        />
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        <div>
          <label for="billing_city" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.city')} *
          </label>
          <input
            id="billing_city"
            type="text"
            bind:value={billingForm.city}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.billing_city}
            placeholder={$_('checkout.city_placeholder')}
            required
          />
          {#if errors.billing_city}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.billing_city}</p>
          {/if}
        </div>

        <div>
          <label for="billing_postal_code" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.postal_code')} *
          </label>
          <input
            id="billing_postal_code"
            type="text"
            bind:value={billingForm.postal_code}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
            class:border-red-500={errors.billing_postal_code}
            placeholder={$_('checkout.postal_code_placeholder')}
            required
          />
          {#if errors.billing_postal_code}
            <p class="mt-1 text-sm text-red-600 font-book-text">{errors.billing_postal_code}</p>
          {/if}
        </div>

        <div>
          <label for="billing_country" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
            {$_('checkout.country')}
          </label>
          <select
            id="billing_country"
            bind:value={billingForm.country_code}
            disabled={isLoading}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          >
            <option value="il">Israel</option>
            <option value="us">United States</option>
            <option value="gb">United Kingdom</option>
            <option value="ca">Canada</option>
          </select>
        </div>
      </div>

      <div class="mt-4">
        <label for="billing_phone" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.phone')}
        </label>
        <input
          id="billing_phone"
          type="tel"
          bind:value={billingForm.phone}
          disabled={isLoading}
          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 font-book-text"
          placeholder={$_('checkout.phone_placeholder')}
        />
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-between pt-6">
      <button
        type="button"
        on:click={onBack}
        disabled={isLoading}
        class="btn-classic-outline"
        class:opacity-50={isLoading}
      >
        ← {$_('checkout.back')}
      </button>

      <button
        type="submit"
        disabled={isLoading}
        class="btn-classic"
        class:opacity-50={isLoading}
      >
        {#if isLoading}
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {$_('checkout.processing')}
          </div>
        {:else}
          {$_('checkout.continue')} →
        {/if}
      </button>
    </div>
  </form>
</div>
