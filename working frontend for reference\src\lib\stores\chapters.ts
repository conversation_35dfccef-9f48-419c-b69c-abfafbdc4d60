import { writable, derived } from 'svelte/store'
import type { Product } from '$lib/types/products'
import { apiClient } from '$lib/api/client'
import { currentRegion } from './currency'

// Chapter type (based on Product with chapter-specific fields)
export interface Chapter extends Omit<Product, 'duration'> {
  // Chapter-specific metadata
  chapter_order?: number
  is_free?: boolean
  chapter_duration?: string  // Renamed to avoid conflict with Product.duration
  chapter_difficulty?: string
  book_collection_id?: string
}

// Helper function to convert MedusaJS product to Chapter
function convertProductToChapter(product: any): Chapter {
  return {
    ...product,
    // Extract chapter-specific metadata
    chapter_order: product.metadata?.chapter_order ? parseInt(product.metadata.chapter_order) : 0,
    is_free: product.metadata?.is_free === 'true' || product.metadata?.is_free === true,
    chapter_duration: product.metadata?.chapter_duration || product.reading_time_minutes ? `${product.reading_time_minutes} min` : '10 min',
    chapter_difficulty: product.metadata?.chapter_difficulty || product.difficulty_level || 'beginner',
    book_collection_id: product.collection_id
  }
}

export interface ChaptersState {
  chapters: Chapter[]
  currentChapter: Chapter | null
  isLoading: boolean
  error: string | null
}

const initialState: ChaptersState = {
  chapters: [],
  currentChapter: null,
  isLoading: false,
  error: null,
}

function createChaptersStore() {
  const { subscribe, set, update } = writable<ChaptersState>(initialState)

  return {
    subscribe,

    // Load chapters for a specific book (collection)
    loadChaptersForBook: async (bookProduct: Product) => {
      update(state => ({ ...state, isLoading: true, error: null }))

      try {
        console.log('📚 Loading chapters for book:', bookProduct.title, bookProduct)

        // Get the collection ID from the book product metadata
        // Handle both normal and quoted metadata keys
        let collectionId = bookProduct.metadata?.chapters_collection_id ||
                          bookProduct.metadata?.['"chapters_collection_id"'] ||
                          bookProduct.collection_id ||
                          `${bookProduct.handle}-chapters`

        // Clean up quoted values
        if (typeof collectionId === 'string' && collectionId.startsWith('"') && collectionId.endsWith('"')) {
          collectionId = collectionId.slice(1, -1)
        }

        console.log('🔍 Book metadata:', bookProduct.metadata)
        console.log('🔍 Book collection_id:', bookProduct.collection_id)
        console.log('🔍 Resolved collection ID:', collectionId)

        if (!collectionId) {
          console.warn('⚠️ No collection ID found for book:', bookProduct.title)
          update(state => ({ ...state, chapters: [], isLoading: false }))
          return { success: true }
        }

        console.log('📂 Looking for chapters in collection:', collectionId)

        // Get current region for pricing
        let regionId: string | undefined = undefined
        currentRegion.subscribe(value => regionId = value?.id)()

        // Fetch chapters (products) from the collection
        const response = await apiClient.getCollectionProducts(collectionId, {
          limit: 100,
          region_id: regionId
        })

        if (response.data && response.data.products) {
          console.log('📖 Found products in collection:', response.data.products.length)

          // Filter to only include chapters (not the book itself), convert to chapters and sort by order
          const chapters = response.data.products
            .filter(product => {
              const productType = product.type?.value?.toLowerCase()
              console.log('🔍 Product:', product.title, 'Type:', productType)
              return productType === 'chapter' // Only include chapters, not books
            })
            .map(convertProductToChapter)
            .sort((a, b) => (a.chapter_order || 0) - (b.chapter_order || 0))

          update(state => ({ ...state, chapters, isLoading: false }))

          console.log('✅ Chapters filtered, loaded and sorted:', chapters.length, 'chapters')
          console.log('📋 Chapter details:', chapters.map(c => ({
            title: c.title,
            type: c.type,
            order: c.chapter_order,
            is_free: c.is_free
          })))

          return { success: true }
        } else {
          console.log('📭 No chapters found for collection:', collectionId)
          update(state => ({ ...state, chapters: [], isLoading: false }))
          return { success: true }
        }
      } catch (error) {
        console.error('❌ Failed to load chapters:', error)
        const errorMessage = error instanceof Error ? error.message : 'Failed to load chapters'
        update(state => ({
          ...state,
          error: `Failed to load chapters: ${errorMessage}`,
          isLoading: false,
          chapters: []
        }))
        return { success: false, error: errorMessage }
      }
    },

    // Set current chapter
    setCurrentChapter: (chapter: Chapter | null) => {
      update(state => ({ ...state, currentChapter: chapter }))
    },

    // Clear current chapter
    clearCurrentChapter: () => {
      update(state => ({
        ...state,
        currentChapter: null,
      }))
    },

    // Clear error
    clearError: () => {
      update(state => ({ ...state, error: null }))
    },

    // Reset store
    reset: () => {
      set(initialState)
    },
  }
}

export const chaptersStore = createChaptersStore()

// Derived stores for computed values
export const freeChapters = derived(
  chaptersStore,
  $chaptersStore => $chaptersStore.chapters.filter(chapter => chapter.is_free)
)

export const premiumChapters = derived(
  chaptersStore,
  $chaptersStore => $chaptersStore.chapters.filter(chapter => !chapter.is_free)
)
