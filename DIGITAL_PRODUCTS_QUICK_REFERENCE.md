# 🚀 Digital Products Quick Reference

## Required Metada<PERSON> Fields (Copy & Paste)

### Basic Setup
```
Key: is_digital_product
Value: true
```

### Single Book (Main Content Only)
```
Key: digital_medias
Value: [{"type":"main","url":"YOUR_FLIPHTML5_URL_HERE","title":"Book Title"}]
```

### Book with Preview
```
Key: digital_medias
Value: [{"type":"preview","url":"PREVIEW_URL","title":"Free Preview"},{"type":"main","url":"FULL_BOOK_URL","title":"Complete Book"}]
```

## 📝 Template Examples

### Hebrew Grammar Book
```json
[{"type":"preview","url":"https://online.fliphtml5.com/xrcwd/preview/","title":"Chapter 1 Preview","description":"Introduction to Hebrew letters"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/full/","title":"Complete Hebrew Grammar","description":"All 15 chapters with exercises"}]
```

### Chapter-based Course
```json
[{"type":"main","url":"https://online.fliphtml5.com/xrcwd/chapter1/","title":"Chapter 1: Basics"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/chapter2/","title":"Chapter 2: Intermediate"}]
```

### Workbook with Solutions
```json
[{"type":"preview","url":"https://online.fliphtml5.com/xrcwd/sample/","title":"Sample Pages"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/workbook/","title":"Complete Workbook"},{"type":"main","url":"https://online.fliphtml5.com/xrcwd/solutions/","title":"Answer Key"}]
```

## 🔧 Quick Setup Steps

1. **Create Product** → Admin Panel → Products → Add Product
2. **Add Metadata** → Scroll to Metadata section
3. **Copy Template** → Use examples above
4. **Replace URLs** → Use your FlipHTML5 URLs
5. **Save Product** → Test with order

## 📖 Getting FlipHTML5 URLs

1. FlipHTML5 → Your Book → Share → Embed
2. Copy iframe src URL: `https://online.fliphtml5.com/xrcwd/qlyk/`
3. Use this URL in metadata

## ✅ Test Checklist

- [ ] Product created with metadata
- [ ] Test order placed
- [ ] Email notification received
- [ ] Book appears in customer library
- [ ] FlipHTML5 viewer loads correctly

## 🆘 Quick Fixes

**Not working?**
- Check `is_digital_product` is exactly `"true"`
- Verify JSON syntax in `digital_medias`
- Test FlipHTML5 URL in browser
- Check backend logs for errors
