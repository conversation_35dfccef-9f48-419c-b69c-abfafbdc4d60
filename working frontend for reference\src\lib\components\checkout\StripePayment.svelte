<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { cartActions } from '$lib/stores/cart'
 	import { formatPriceWithCurrency } from '$lib/stores/currency'


  export let cart: any
  export let onComplete: (result: { success: boolean, order?: any, error?: string }) => void
  export let onBack: () => void

  let stripeLoaded = false
  let stripe: any = null
  let elements: any = null
  let cardElement: any = null
  let isProcessing = false
  let paymentError = ''
  let clientSecret = ''
  let elementsInitialized = false

  // Get Stripe publishable key from environment
  const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY

  // Reactive statement to initialize elements when everything is ready
  $: if (stripeLoaded && clientSecret && !elementsInitialized && typeof document !== 'undefined') {
    // Check if the DOM element exists
    const cardElementDiv = document.getElementById('card-element')
    if (cardElementDiv) {
      console.log('🔄 DOM element found, initializing Stripe Elements')
      initializeElements()
      elementsInitialized = true
    }
  }

  onMount(async () => {
    await loadStripe()
    extractClientSecret()
  })

  async function loadStripe() {
    try {
      // Dynamically import Stripe
      const { loadStripe } = await import('@stripe/stripe-js')

      if (!stripePublishableKey) {
        throw new Error('Stripe publishable key not found')
      }

      stripe = await loadStripe(stripePublishableKey)

      if (!stripe) {
        throw new Error('Failed to load Stripe')
      }

      stripeLoaded = true
      console.log('✅ Stripe loaded successfully')
    } catch (error) {
      console.error('Failed to load Stripe:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  function extractClientSecret() {
    // Extract client secret from payment session based on the console output structure
    console.log('🔍 Full cart object:', cart)
    console.log('🔍 Payment collection:', cart?.payment_collection)

    const paymentSession = cart?.payment_collection?.payment_sessions?.[0]
    console.log('🔍 First payment session:', paymentSession)

    // Based on console output: paymentSession.data.client_secret
    if (paymentSession?.data?.client_secret) {
      clientSecret = paymentSession.data.client_secret
      console.log('✅ Client secret found:', clientSecret.substring(0, 20) + '...')
    } else {
      console.warn('⚠️ No client secret found in payment session')
      console.log('🔍 Payment session data:', paymentSession?.data)
      paymentError = $_('checkout.no_client_secret')
    }
  }

  async function createCheckoutSession() {
    try {
      if (!cart?.id) {
        throw new Error('No cart found')
      }

      console.log('� Creating Stripe Checkout Session for cart:', cart.id)

      // Create checkout session via our backend API
      const response = await apiClient.createStripeCheckoutSession(cart.id, {
        success_url: `${window.location.origin}/orders/{CHECKOUT_SESSION_ID}/confirmation`,
        cancel_url: `${window.location.origin}/checkout`
      })

      if (response.data?.checkout_session?.id) {
        checkoutSessionId = response.data.checkout_session.id
        console.log('✅ Checkout session created:', checkoutSessionId)

        // Initialize embedded checkout
        await initializeEmbeddedCheckout()
      } else {
        throw new Error('Failed to create checkout session')
      }
    } catch (error) {
      console.error('❌ Failed to create checkout session:', error)
      paymentError = $_('checkout.failed_to_initialize_payment')
    }
  }

  async function initializeEmbeddedCheckout() {
    if (!stripe || !checkoutSessionId) {
      console.warn('⚠️ Stripe not loaded or no checkout session ID')
      return
    }

    try {
      console.log('🔄 Initializing Stripe Embedded Checkout')

      // Initialize embedded checkout
      checkout = await stripe.initEmbeddedCheckout({
        clientSecret: checkoutSessionId
      })

      // Mount the checkout
      checkout.mount('#checkout-element')

      console.log('✅ Stripe Embedded Checkout initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Stripe Embedded Checkout:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  // With embedded checkout, payment handling is done by Stripe
  // We just need to listen for completion events
  async function handleCheckoutComplete() {
    try {
      console.log('✅ Checkout completed, processing order')

      // Complete the cart
      const completeResponse = await apiClient.completeCart(cart.id)

      if (completeResponse.data?.type === 'order' && completeResponse.data.order) {
        // Order placed successfully
        console.log('✅ Order placed successfully:', completeResponse.data.order.id)
        cartActions.initialize() // Refresh cart
        onComplete({
          success: true,
          order: completeResponse.data.order
        })
      } else {
        console.error('❌ Cart completion failed')
        const errorMsg = $_('checkout.order_completion_failed')
        onComplete({ success: false, error: errorMsg })
      }
    } catch (error) {
      console.error('❌ Order completion error:', error)
      onComplete({ success: false, error: $_('checkout.payment_processing_error') })
    }
  }
</script>

<div class="space-y-6">
  {#if !stripeLoaded}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
      <p class="font-book-text" style="color: var(--color-text-secondary);">
        {$_('checkout.loading_payment_form')}
      </p>
    </div>
  {:else if paymentError}
    <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-center">
        <span class="text-red-500 text-xl mr-3">⚠️</span>
        <div>
          <h4 class="font-book-title text-sm font-semibold text-red-800">
            {$_('checkout.payment_error')}
          </h4>
          <p class="font-book-text text-sm text-red-700 mt-1">
            {paymentError}
          </p>
        </div>
      </div>
    </div>
  {/if}

  {#if stripeLoaded && checkoutSessionId}
    <!-- Stripe Embedded Checkout -->
    <div class="space-y-4">
      <div>
        <h4 class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.card_details')} *
        </h4>
        <div class="border border-gray-300 rounded-lg bg-white">
          <div id="checkout-element">
            <!-- Stripe Embedded Checkout will render here -->
          </div>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
        <span class="text-green-500 mr-2">🔒</span>
        <span class="font-book-text">
          {$_('checkout.secure_payment_notice')}
        </span>
      </div>

      <!-- Order Summary -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="font-book-title text-sm font-semibold mb-2" style="color: var(--color-text-primary);">
          {$_('checkout.payment_summary')}
        </h4>
        <div class="space-y-1 text-sm">
          <div class="flex justify-between">
            <span class="font-book-text" style="color: var(--color-text-secondary);">
              {$_('checkout.subtotal')}
            </span>
            <span class="font-book-text" style="color: var(--color-text-primary);">
              {cart.subtotal ? formatPriceWithCurrency(cart.subtotal) : cart.total_amount ? 
                formatPriceWithCurrency(cart.total_amount) : '0.00'}
            </span>
          </div>
          {#if cart.shipping_total}
            <div class="flex justify-between">
              <span class="font-book-text" style="color: var(--color-text-secondary);">
                {$_('checkout.shipping')}
              </span>
              <span class="font-book-text" style="color: var(--color-text-primary);">
                {formatPriceWithCurrency(cart.shipping_total)} 
              </span>
            </div>
          {/if}
          {#if cart.tax_total}
            <div class="flex justify-between">
              <span class="font-book-text" style="color: var(--color-text-secondary);">
                {$_('checkout.tax')}
              </span>
              <span class="font-book-text" style="color: var(--color-text-primary);">
                {formatPriceWithCurrency(cart.tax_total)}
              </span>
            </div>
          {/if}
          <div class="flex justify-between font-semibold text-base border-t pt-2">
            <span class="font-book-title" style="color: var(--color-text-primary);">
              {$_('checkout.total')}
            </span>
            <span class="font-book-title" style="color: var(--color-text-primary);">
              {cart.total ? formatPriceWithCurrency(cart.total) : cart.total_amount ? 
                formatPriceWithCurrency(cart.total_amount) : '0.00'}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Back Button Only - Embedded Checkout handles payment -->
    <div class="flex justify-start pt-4">
      <button
        type="button"
        on:click={onBack}
        class="btn-classic-outline"
      >
        ← {$_('checkout.back')}
      </button>
    </div>
  {/if}
</div>
