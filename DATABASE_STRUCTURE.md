# 🗄️ Hebrew Book Store - Database Structure

## 📋 Overview

This document provides a comprehensive overview of the PostgreSQL database structure for the Hebrew Book Store project built with MedusaJS v2.

## 🔧 Database Configuration

### Connection
- **Database**: PostgreSQL
- **Configuration**: `backend/medusa-config.ts`
- **Environment Variable**: `DATABASE_URL`
- **Example**: `postgresql://username:password@localhost:5432/medusa_db`

### ORM
- **Framework**: MikroORM (used by MedusaJS v2)
- **Migrations**: Located in `backend/src/modules/*/migrations/`

## 📊 Database Schema

### 🏪 Core MedusaJS Tables

MedusaJS v2 automatically creates these core commerce tables:

```sql
-- Products and Variants
product                    -- Main product information
product_variant           -- Product variants (size, color, etc.)
product_option           -- Product options (size, color)
product_option_value     -- Option values (small, medium, large)
product_image           -- Product images
product_tag             -- Product tags
product_type            -- Product types (Book, Subscription, etc.)
product_collection      -- Product collections/categories

-- Pricing and Inventory
price_set               -- Price management
price                   -- Individual prices
money_amount           -- Price amounts with currency
inventory_item         -- Inventory tracking
inventory_level        -- Stock levels per location
stock_location         -- Warehouse/store locations

-- Orders and Cart
cart                   -- Shopping carts
line_item             -- Cart/order line items
order                 -- Customer orders
payment               -- Payment information
payment_session      -- Payment processing sessions
fulfillment          -- Order fulfillment

-- Customers and Auth
customer              -- Customer accounts
customer_group        -- Customer groups
address              -- Customer addresses
user                 -- Admin users
auth_user            -- Authentication

-- Regional Settings
region               -- Sales regions
country              -- Countries
currency             -- Supported currencies
tax_rate            -- Tax rates per region

-- Sales Channels
sales_channel        -- Different sales channels
publishable_api_key  -- API keys for frontend access
```

### 📚 Custom Digital Product Module

Located in `backend/src/modules/digital-product/`:

```sql
-- Digital Products (Custom Module)
CREATE TABLE digital_product (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ NULL
);

-- Digital Product Media (FlipHTML5 URLs, Files)
CREATE TABLE digital_product_media (
    id TEXT PRIMARY KEY,
    type TEXT CHECK (type IN ('main', 'preview')) NOT NULL,
    fileId TEXT NULL,                    -- File ID for uploaded files
    mimeType TEXT NULL,                  -- MIME type
    url TEXT NULL,                       -- FlipHTML5 URL or direct file URL
    digital_product_id TEXT NOT NULL,   -- FK to digital_product
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ NULL,
    
    FOREIGN KEY (digital_product_id) REFERENCES digital_product(id) ON DELETE CASCADE
);

-- Digital Product Orders (Order Fulfillment)
CREATE TABLE digital_product_order (
    id TEXT PRIMARY KEY,
    status TEXT CHECK (status IN ('pending', 'sent')) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ NULL
);

-- Many-to-Many: Digital Products ↔ Orders
CREATE TABLE digitalproduct_digitalproductorders (
    digital_product_order_id TEXT NOT NULL,
    digital_product_id TEXT NOT NULL,
    PRIMARY KEY (digital_product_order_id, digital_product_id),
    
    FOREIGN KEY (digital_product_order_id) REFERENCES digital_product_order(id) ON DELETE CASCADE,
    FOREIGN KEY (digital_product_id) REFERENCES digital_product(id) ON DELETE CASCADE
);
```

### 🔗 Module Links (Relations)

MedusaJS v2 uses Module Links to connect different modules:

```typescript
// Digital Product ↔ Product Variant Link
// File: backend/src/links/digital-product-variant.ts
defineLink(
  DigitalProductModule.linkable.digitalProduct,
  ProductModule.linkable.productVariant
)
```

This creates a relationship table:
```sql
-- Auto-generated by MedusaJS
digital_product_product_variant_link (
    digital_product_id TEXT,
    product_variant_id TEXT
)
```

## 📝 SQL Queries for Products

### 1. Fetch All Products with Variants and Pricing

```sql
-- Basic product query with variants
SELECT 
    p.id,
    p.title,
    p.description,
    p.handle,
    p.status,
    p.thumbnail,
    p.metadata,
    pv.id as variant_id,
    pv.title as variant_title,
    pv.sku,
    pv.manage_inventory,
    pv.inventory_quantity
FROM product p
LEFT JOIN product_variant pv ON p.id = pv.product_id
WHERE p.deleted_at IS NULL 
  AND pv.deleted_at IS NULL
ORDER BY p.created_at DESC;
```

### 2. Fetch Products with Calculated Prices

```sql
-- Products with pricing for specific region
SELECT 
    p.id,
    p.title,
    p.description,
    p.handle,
    p.thumbnail,
    pv.id as variant_id,
    pv.title as variant_title,
    pv.sku,
    ps.id as price_set_id,
    pr.amount,
    pr.currency_code
FROM product p
LEFT JOIN product_variant pv ON p.id = pv.product_id
LEFT JOIN price_set ps ON pv.price_set_id = ps.id
LEFT JOIN price pr ON ps.id = pr.price_set_id
LEFT JOIN money_amount ma ON pr.id = ma.price_id
WHERE p.deleted_at IS NULL 
  AND pv.deleted_at IS NULL
  AND pr.currency_code = 'ILS'  -- Israeli Shekel
ORDER BY p.created_at DESC;
```

### 3. Fetch Products by Type (Books Only)

```sql
-- Filter products by type
SELECT 
    p.id,
    p.title,
    p.description,
    p.handle,
    p.thumbnail,
    pt.value as product_type
FROM product p
LEFT JOIN product_type pt ON p.type_id = pt.id
WHERE p.deleted_at IS NULL 
  AND pt.value = 'Book'
ORDER BY p.created_at DESC;
```

### 4. Fetch Products with Collections (Chapters)

```sql
-- Products with their collections
SELECT 
    p.id,
    p.title,
    p.description,
    p.handle,
    p.metadata,
    pc.id as collection_id,
    pc.title as collection_title,
    pc.handle as collection_handle
FROM product p
LEFT JOIN product_collection_product pcp ON p.id = pcp.product_id
LEFT JOIN product_collection pc ON pcp.product_collection_id = pc.id
WHERE p.deleted_at IS NULL 
  AND pc.deleted_at IS NULL
ORDER BY p.created_at DESC;
```

### 5. Fetch Digital Products with Linked Product Variants

```sql
-- Digital products with their linked regular products
SELECT 
    dp.id as digital_product_id,
    dp.name as digital_product_name,
    dpm.type as media_type,
    dpm.url as media_url,
    pv.id as variant_id,
    pv.title as variant_title,
    pv.sku,
    p.id as product_id,
    p.title as product_title,
    p.handle as product_handle
FROM digital_product dp
LEFT JOIN digital_product_media dpm ON dp.id = dpm.digital_product_id
LEFT JOIN digital_product_product_variant_link dpvl ON dp.id = dpvl.digital_product_id
LEFT JOIN product_variant pv ON dpvl.product_variant_id = pv.id
LEFT JOIN product p ON pv.product_id = p.id
WHERE dp.deleted_at IS NULL 
  AND dpm.deleted_at IS NULL
  AND pv.deleted_at IS NULL
  AND p.deleted_at IS NULL
ORDER BY dp.created_at DESC;
```

## 🔍 Advanced Queries

### Product Search with Metadata

```sql
-- Search products with metadata filtering
SELECT 
    p.id,
    p.title,
    p.description,
    p.handle,
    p.metadata,
    p.thumbnail
FROM product p
WHERE p.deleted_at IS NULL 
  AND (
    p.title ILIKE '%hebrew%' 
    OR p.description ILIKE '%hebrew%'
    OR p.metadata->>'chapters_collection_id' IS NOT NULL
  )
ORDER BY p.created_at DESC;
```

### Inventory Levels

```sql
-- Check inventory levels
SELECT 
    p.title,
    pv.sku,
    pv.manage_inventory,
    pv.inventory_quantity,
    il.stocked_quantity,
    il.reserved_quantity,
    il.incoming_quantity,
    sl.name as location_name
FROM product p
JOIN product_variant pv ON p.id = pv.product_id
LEFT JOIN inventory_item ii ON pv.inventory_item_id = ii.id
LEFT JOIN inventory_level il ON ii.id = il.inventory_item_id
LEFT JOIN stock_location sl ON il.location_id = sl.id
WHERE p.deleted_at IS NULL 
  AND pv.deleted_at IS NULL
ORDER BY p.title;
```

## 📊 Performance Indexes

Key indexes for optimal query performance:

```sql
-- Product indexes
CREATE INDEX idx_product_status ON product(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_product_type ON product(type_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_product_handle ON product(handle) WHERE deleted_at IS NULL;

-- Product variant indexes  
CREATE INDEX idx_variant_product ON product_variant(product_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_variant_sku ON product_variant(sku) WHERE deleted_at IS NULL;

-- Digital product indexes
CREATE INDEX idx_digital_product_media_type ON digital_product_media(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_digital_product_media_product ON digital_product_media(digital_product_id) WHERE deleted_at IS NULL;
```

## 🚀 Next Steps

1. **API Queries**: Use MedusaJS Admin API endpoints for data access
2. **GraphQL**: Leverage MedusaJS v2 GraphQL capabilities
3. **Query Builder**: Use MedusaJS query builder for complex queries
4. **Caching**: Implement Redis caching for frequently accessed data

For API examples, see the next section on product fetching requests.
