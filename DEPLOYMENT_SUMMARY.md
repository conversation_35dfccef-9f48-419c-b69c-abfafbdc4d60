# 🚀 MedusaJS Azure Deployment - Quick Summary

## ✅ **What We Have Working**

### **Architecture:**
- **GitHub Actions** → **Azure App Service** → **Azure PostgreSQL**
- **Built-in MedusaJS Admin Panel** (no separate deployment needed)
- **Automatic CI/CD** on push to main branch

### **Key Files:**
- `backend/startup.sh` - Official MedusaJS startup process
- `.github/workflows/main_hebrew-book.yml` - GitHub Actions workflow
- `backend/package.json` - Build and start scripts

### **Deployment Process:**
1. **GitHub Actions builds** MedusaJS (`yarn build` creates `.medusa/server`)
2. **Packages built files** with runtime dependencies
3. **Deploys to Azure App Service** (Linux, Node.js 20)
4. **Azure runs startup.sh** which:
   - Installs production dependencies
   - Runs database migrations
   - Starts MedusaJS server

---

## 🔑 **Required Environment Variables**

### **Essential (Required):**
```bash
DATABASE_URL=postgresql://user:<EMAIL>:5432/db?sslmode=require
JWT_SECRET=your-32-byte-base64-secret
COOKIE_SECRET=your-32-byte-base64-secret
MEDUSA_BACKEND_URL=https://your-app.azurewebsites.net
ADMIN_CORS=https://your-app.azurewebsites.net
```

### **Payment (Stripe):**
```bash
STRIPE_API_KEY=sk_live_your_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### **Optional:**
```bash
STORE_CORS=https://your-frontend-domain.com
SENDGRID_API_KEY=SG.your-sendgrid-key
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
```

---

## 📋 **Quick Setup Checklist**

### **1. Azure Resources:**
- [ ] Create Azure Database for PostgreSQL
- [ ] Create Azure App Service (Linux, Node.js 20)
- [ ] Configure firewall rules for database
- [ ] Set environment variables in App Service

### **2. GitHub Setup:**
- [ ] Add Azure credentials to GitHub Secrets
- [ ] Create GitHub Actions workflow
- [ ] Ensure `startup.sh` is executable

### **3. Database:**
- [ ] Run migrations manually first time: `npx medusa migrations run`
- [ ] Create admin user: `npx medusa user -e <EMAIL> -p password`

### **4. Testing:**
- [ ] Backend API: `https://your-app.azurewebsites.net/health`
- [ ] Admin Panel: `https://your-app.azurewebsites.net/app`
- [ ] Store API: `https://your-app.azurewebsites.net/store/products`

---

## 🎯 **Access Points**

- **Backend API**: `https://your-app.azurewebsites.net`
- **Admin Panel**: `https://your-app.azurewebsites.net/app`
- **Health Check**: `https://your-app.azurewebsites.net/health`

---

## 🔧 **Troubleshooting Commands**

```bash
# View logs
az webapp log tail --name your-app --resource-group your-rg

# SSH into app
az webapp ssh --name your-app --resource-group your-rg

# Update environment variables
az webapp config appsettings set --name your-app --resource-group your-rg --settings KEY=value
```

---

## 📚 **Key Insights from Our Setup**

1. **Use yarn consistently** throughout the process
2. **Follow official MedusaJS deployment guide**: `cd .medusa/server && yarn install && yarn predeploy && yarn start`
3. **Build once in GitHub Actions**, deploy built files to Azure
4. **Don't copy tsconfig.json to runtime directory**
5. **Pre-install dependencies during build** for faster startup
6. **Use startup.sh script** for reliable Azure App Service startup

---

**✅ This setup successfully deploys MedusaJS v2.8.4 with built-in admin panel to Azure App Service using GitHub Actions.**
