import { Migration } from '@mikro-orm/migrations';

export class Migration20250713112704 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "digital_product_media" alter column "fileId" type text using ("fileId"::text);`);
    this.addSql(`alter table if exists "digital_product_media" alter column "fileId" drop not null;`);
    this.addSql(`alter table if exists "digital_product_media" alter column "mimeType" type text using ("mimeType"::text);`);
    this.addSql(`alter table if exists "digital_product_media" alter column "mimeType" drop not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "digital_product_media" alter column "fileId" type text using ("fileId"::text);`);
    this.addSql(`alter table if exists "digital_product_media" alter column "fileId" set not null;`);
    this.addSql(`alter table if exists "digital_product_media" alter column "mimeType" type text using ("mimeType"::text);`);
    this.addSql(`alter table if exists "digital_product_media" alter column "mimeType" set not null;`);
  }

}
