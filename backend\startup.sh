#!/bin/bash

# Official MedusaJS startup script based on documentation
# https://docs.medusajs.com/learn/deployment/general

export NODE_ENV=production
export YARN_SILENT=1

echo "🚀 MedusaJS Official Startup Process - $(date)"
echo "📂 Working directory: $(pwd)"

# Check directory contents
echo "📁 Directory contents:"
ls -la

# Official MedusaJS deployment process
if [ -d ".medusa/server" ]; then
    echo "✅ Found .medusa/server - following official deployment guide"
    echo "📋 Official command: cd .medusa/server && yarn install && yarn predeploy && yarn start"

    cd .medusa/server
    echo "� Now in: $(pwd)"
    echo "📁 Built server contents:"
    ls -la

    # Remove any tsconfig.json that shouldn't be here
    if [ -f "tsconfig.json" ]; then
        echo "⚠️  Removing tsconfig.json from runtime directory"
        rm tsconfig.json
    fi

    echo "📦 Checking production dependencies..."
    # Check if dependencies are already installed
    if [ -d "node_modules" ]; then
        echo "✅ Dependencies already installed, skipping installation"
    elif [ -f "package.json" ]; then
        echo "✅ Found package.json, installing dependencies..."
        # Use registry with retry and timeout settings
        yarn install --production --frozen-lockfile --silent --registry https://registry.npmjs.org/ --network-timeout 300000 --network-concurrency 1
    else
        echo "❌ No package.json found in .medusa/server"
        echo "⚠️  Using parent directory dependencies..."
        cd ..
        if [ ! -d "node_modules" ]; then
            yarn install --production --frozen-lockfile --silent --registry https://registry.npmjs.org/ --network-timeout 300000 --network-concurrency 1
        else
            echo "✅ Parent dependencies already installed"
        fi
        cd server
    fi

    echo "🔄 Running predeploy (migrations)..."
    echo "📋 Migration needs to run from root directory where source files are available"

    # Run migrations from parent directory where source files and all dependencies are available
    cd ..
    echo "📂 Now in: $(pwd)"

    # Ensure root dependencies are available for migration
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing root dependencies for migration..."
        yarn install --frozen-lockfile --silent --registry https://registry.npmjs.org/ --network-timeout 300000 --network-concurrency 1
    fi

    echo "🔄 Running migration from root directory..."
    yarn predeploy || echo "⚠️  Predeploy failed, continuing..."

    # Go back to server directory for startup
    cd .medusa/server

    echo "🎯 Starting MedusaJS server..."
    exec yarn start

else
    echo "❌ No .medusa/server directory found!"
    echo "⚠️  This means the build process didn't work correctly."
    echo "📋 Attempting fallback startup from root..."

    if [ -f "package.json" ]; then
        echo "📦 Installing dependencies..."
        yarn install --frozen-lockfile --silent --registry https://registry.npmjs.org/ --network-timeout 300000 --network-concurrency 1

        echo "🔄 Running predeploy..."
        yarn predeploy || echo "⚠️  Predeploy failed, continuing..."

        echo "🎯 Starting from root directory..."
        exec yarn start
    else
        echo "❌ No package.json found - cannot start application"
        exit 1
    fi
fi
