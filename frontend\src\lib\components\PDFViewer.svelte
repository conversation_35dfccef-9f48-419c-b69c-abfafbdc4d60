<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { browser } from '$app/environment'
  
  export let pdfUrl: string = ''
  export let title: string = 'PDF Document'
  export let height: string = '600px'
  export let initialPage: number = 1
  export let initialZoom: string = 'page-width'
  export let showToolbar: boolean = true
  export let enableSearch: boolean = true
  export let enableDownload: boolean = true
  export let enablePrint: boolean = true

  let pdfContainer: HTMLDivElement
  let pdfViewer: any = null
  let isLoading = true
  let error = ''
  let currentPage = initialPage
  let totalPages = 0
  let zoomLevel = initialZoom

  // PDF.js instance
  let pdfjsLib: any = null

  onMount(async () => {
    if (!browser || !pdfUrl) return

    try {
      // Import PDF.js
      pdfjsLib = await import('pdfjs-dist')
      
      // Set worker source
      pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`
      
      await loadPDF()
    } catch (err) {
      console.error('❌ Failed to initialize PDF.js:', err)
      error = 'Failed to load PDF viewer'
      isLoading = false
    }
  })

  async function loadPDF() {
    if (!pdfjsLib || !pdfUrl) return

    try {
      isLoading = true
      error = ''

      console.log('📄 Loading PDF:', pdfUrl)

      // Load the PDF document
      const loadingTask = pdfjsLib.getDocument(pdfUrl)
      const pdf = await loadingTask.promise

      totalPages = pdf.numPages
      console.log('📄 PDF loaded successfully. Pages:', totalPages)

      // Render the first page
      await renderPage(pdf, currentPage)
      
      isLoading = false
    } catch (err) {
      console.error('❌ Failed to load PDF:', err)
      error = 'Failed to load PDF document'
      isLoading = false
    }
  }

  async function renderPage(pdf: any, pageNumber: number) {
    try {
      const page = await pdf.getPage(pageNumber)
      const scale = getScaleForZoom()
      const viewport = page.getViewport({ scale })

      // Clear previous content
      if (pdfContainer) {
        pdfContainer.innerHTML = ''
      }

      // Create canvas
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.height = viewport.height
      canvas.width = viewport.width

      // Add canvas to container
      if (pdfContainer) {
        pdfContainer.appendChild(canvas)
      }

      // Render PDF page into canvas context
      const renderContext = {
        canvasContext: context,
        viewport: viewport
      }

      await page.render(renderContext).promise
      console.log('📄 Page rendered:', pageNumber)
    } catch (err) {
      console.error('❌ Failed to render page:', err)
      error = 'Failed to render PDF page'
    }
  }

  function getScaleForZoom(): number {
    switch (zoomLevel) {
      case 'page-width':
        return 1.0 // Will be calculated based on container width
      case 'page-height':
        return 1.2
      case 'page-fit':
        return 0.8
      case 'auto':
        return 1.0
      default:
        return parseFloat(zoomLevel) || 1.0
    }
  }

  async function goToPage(pageNum: number) {
    if (!pdfjsLib || pageNum < 1 || pageNum > totalPages) return
    
    currentPage = pageNum
    
    try {
      const loadingTask = pdfjsLib.getDocument(pdfUrl)
      const pdf = await loadingTask.promise
      await renderPage(pdf, currentPage)
    } catch (err) {
      console.error('❌ Failed to navigate to page:', err)
    }
  }

  async function changeZoom(newZoom: string) {
    zoomLevel = newZoom
    
    if (!pdfjsLib) return
    
    try {
      const loadingTask = pdfjsLib.getDocument(pdfUrl)
      const pdf = await loadingTask.promise
      await renderPage(pdf, currentPage)
    } catch (err) {
      console.error('❌ Failed to change zoom:', err)
    }
  }

  function nextPage() {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1)
    }
  }

  function prevPage() {
    if (currentPage > 1) {
      goToPage(currentPage - 1)
    }
  }

  onDestroy(() => {
    // Cleanup if needed
    pdfViewer = null
  })

  // Reactive statements
  $: if (pdfUrl && pdfjsLib) {
    loadPDF()
  }
</script>

<div class="pdf-viewer-container" style="height: {height};">
  <!-- PDF Toolbar -->
  {#if showToolbar}
    <div class="pdf-toolbar" style="background: var(--color-bg-secondary); border-bottom: 1px solid var(--color-border); padding: 8px 16px;">
      <div class="flex items-center justify-between">
        <!-- Navigation Controls -->
        <div class="flex items-center gap-2">
          <button
            on:click={prevPage}
            disabled={currentPage <= 1 || isLoading}
            class="pdf-btn"
            title="Previous Page"
          >
            ←
          </button>
          
          <span class="text-sm" style="color: var(--color-text-secondary);">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            on:click={nextPage}
            disabled={currentPage >= totalPages || isLoading}
            class="pdf-btn"
            title="Next Page"
          >
            →
          </button>
        </div>

        <!-- Zoom Controls -->
        <div class="flex items-center gap-2">
          <select
            bind:value={zoomLevel}
            on:change={() => changeZoom(zoomLevel)}
            class="pdf-select"
            disabled={isLoading}
          >
            <option value="page-width">Fit Width</option>
            <option value="page-height">Fit Height</option>
            <option value="page-fit">Fit Page</option>
            <option value="0.5">50%</option>
            <option value="0.75">75%</option>
            <option value="1.0">100%</option>
            <option value="1.25">125%</option>
            <option value="1.5">150%</option>
            <option value="2.0">200%</option>
          </select>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center gap-2">
          {#if enableDownload}
            <a
              href={pdfUrl}
              download
              class="pdf-btn"
              title="Download PDF"
            >
              ⬇
            </a>
          {/if}
          
          {#if enablePrint}
            <button
              on:click={() => window.open(pdfUrl)}
              class="pdf-btn"
              title="Open in New Tab"
            >
              ⧉
            </button>
          {/if}
        </div>
      </div>
    </div>
  {/if}

  <!-- PDF Content -->
  <div class="pdf-content" style="height: calc(100% - {showToolbar ? '48px' : '0px'}); overflow: auto;">
    {#if isLoading}
      <div class="pdf-loading">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
        <p style="color: var(--color-text-secondary);">Loading PDF...</p>
      </div>
    {:else if error}
      <div class="pdf-error">
        <p style="color: var(--color-error);">❌ {error}</p>
        <button on:click={loadPDF} class="pdf-btn mt-2">Retry</button>
      </div>
    {:else}
      <div
        bind:this={pdfContainer}
        class="pdf-container"
        style="display: flex; justify-content: center; padding: 16px;"
      ></div>
    {/if}
  </div>
</div>

<style>
  .pdf-viewer-container {
    border: 1px solid var(--color-border);
    border-radius: 8px;
    overflow: hidden;
    background: var(--color-bg-primary);
  }

  .pdf-toolbar {
    border-bottom: 1px solid var(--color-border);
  }

  .pdf-btn {
    padding: 4px 8px;
    border: 1px solid var(--color-border);
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .pdf-btn:hover:not(:disabled) {
    background: var(--color-bg-secondary);
    border-color: var(--color-primary);
  }

  .pdf-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pdf-select {
    padding: 4px 8px;
    border: 1px solid var(--color-border);
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    border-radius: 4px;
    font-size: 14px;
  }

  .pdf-loading,
  .pdf-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
  }

  .pdf-container {
    min-height: 100%;
  }

  .pdf-container canvas {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }
</style>
