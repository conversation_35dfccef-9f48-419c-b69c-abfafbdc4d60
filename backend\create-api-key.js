const { MedusaApp } = require('@medusajs/framework')
const { loadEnv } = require('@medusajs/framework/utils')

// Load environment variables
loadEnv(process.env.NODE_ENV || 'development', process.cwd())

async function createPublishableApiKey() {
  try {
    console.log('🔑 Creating publishable API key...')
    
    // Initialize Medusa app
    const app = await MedusaApp({
      directory: process.cwd(),
    })
    
    // Get the API key module
    const apiKeyModule = app.modules.apiKey
    
    // Create a publishable API key
    const apiKey = await apiKeyModule.createApiKeys({
      title: 'Storefront Key',
      type: 'publishable',
      created_by: 'system'
    })
    
    console.log('✅ Publishable API key created successfully!')
    console.log('📋 API Key Details:')
    console.log(`   ID: ${apiKey.id}`)
    console.log(`   Title: ${apiKey.title}`)
    console.log(`   Token: ${apiKey.token}`)
    console.log(`   Type: ${apiKey.type}`)
    
    console.log('\n🔧 Add this to your frontend/.env.local file:')
    console.log(`PUBLIC_MEDUSA_PUBLISHABLE_KEY=${apiKey.token}`)
    
    // Get sales channels to associate with the API key
    const salesChannelModule = app.modules.salesChannel
    const salesChannels = await salesChannelModule.listSalesChannels()
    
    if (salesChannels.length > 0) {
      console.log('\n📦 Available Sales Channels:')
      salesChannels.forEach(channel => {
        console.log(`   - ${channel.name} (ID: ${channel.id})`)
      })
      
      // Associate the API key with the first sales channel
      const firstChannel = salesChannels[0]
      await apiKeyModule.updateApiKeys(apiKey.id, {
        sales_channels: [{ id: firstChannel.id }]
      })
      
      console.log(`\n✅ API key associated with sales channel: ${firstChannel.name}`)
    } else {
      console.log('\n⚠️  No sales channels found. You may need to create one in the admin.')
    }
    
    process.exit(0)
  } catch (error) {
    console.error('❌ Error creating API key:', error.message)
    console.error('Full error:', error)
    process.exit(1)
  }
}

createPublishableApiKey()
