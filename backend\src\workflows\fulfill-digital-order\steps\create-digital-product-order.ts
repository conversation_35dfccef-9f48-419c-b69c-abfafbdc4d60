import {
  createStep,
  StepResponse,
} from "@medusajs/framework/workflows-sdk"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import { DIGITAL_PRODUCT_MODULE } from "../../../modules/digital-product"
import DigitalProductModuleService from "../../../modules/digital-product/service"
import { OrderStatus } from "../../../modules/digital-product/types"

export type CreateDigitalProductOrderStepInput = {
  order_id: string
  digital_products: Array<{
    id: string
    name: string
    medias?: any[]
    quantity: number
    variant_title?: string
    product_title?: string
  }>
}

const createDigitalProductOrderStep = createStep(
  "create-digital-product-order-step",
  async (data: CreateDigitalProductOrderStepInput, { container }) => {
    const digitalProductModuleService: DigitalProductModuleService =
      container.resolve(DIGITAL_PRODUCT_MODULE)

    console.log('🔄 Creating digital product order for:', data.order_id)

    let digitalProductIds: string[] = []

    // If digital_products is empty, look up the order to find digital products
    if (data.digital_products.length === 0) {
      console.log('🔍 Looking up digital products from order...')
      const query = container.resolve(ContainerRegistrationKeys.QUERY)

      const { data: orders } = await query.graph({
        entity: "order",
        fields: [
          "items.variant.digital_product.id",
        ],
        filters: {
          id: data.order_id,
        },
      })

      if (orders[0]?.items) {
        orders[0].items.forEach((item: any) => {
          if (item?.variant?.digital_product?.id) {
            digitalProductIds.push(item.variant.digital_product.id)
          }
        })
      }
    } else {
      // Use provided digital products
      digitalProductIds = data.digital_products.map(product => product.id)
    }

    console.log('🔄 Digital product IDs to include:', digitalProductIds)

    if (digitalProductIds.length === 0) {
      throw new Error("No digital products found for order")
    }

    // Create digital product order
    const digitalProductOrder = await digitalProductModuleService
      .createDigitalProductOrders({
        status: OrderStatus.PENDING,
        products: digitalProductIds,
      })

    console.log('✅ Digital product order created:', digitalProductOrder.id)
    console.log('✅ Linked products:', digitalProductIds.length)

    return new StepResponse({
      digital_product_order: digitalProductOrder,
    }, {
      digital_product_order: digitalProductOrder,
    })
  },
  async (compensationData, { container }) => {
    const digitalProductModuleService: DigitalProductModuleService =
      container.resolve(DIGITAL_PRODUCT_MODULE)

    if (compensationData?.digital_product_order?.id) {
      console.log('🔄 Rolling back digital product order:', compensationData.digital_product_order.id)
      await digitalProductModuleService.deleteDigitalProductOrders(
        compensationData.digital_product_order.id
      )
      console.log('✅ Digital product order rollback completed')
    }
  }
)

export { createDigitalProductOrderStep }
export default createDigitalProductOrderStep
