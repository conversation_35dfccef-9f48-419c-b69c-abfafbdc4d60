import { writable, derived } from 'svelte/store'
import { formatPrice as formatPriceUtil } from '$lib/utils/currency'
import { currentCurrency, currentRegion } from './products'

/**
 * Currency formatting store that provides reactive currency formatting
 */

// Export the currency stores from products for convenience
export { currentCurrency, currentRegion } from './products'

/**
 * Reactive price formatter that uses the current currency
 */
export const formatPrice = derived(
  currentCurrency,
  ($currentCurrency) => {
    return (amount: number, options?: { showSymbol?: boolean; showCode?: boolean; locale?: string }) => {
      return formatPriceUtil(amount , $currentCurrency, options) // Convert to cents for the utility
    }
  }
)

/**
 * Format a price with the current currency (for use in components)
 * @param amount - Price amount in main currency units (e.g., 10.99)
 * @param currency - Optional currency override
 */
export function formatPriceWithCurrency(amount: number, currency?: string): string {
  // Get current currency from store
  let currentCurrencyValue = 'USD'
  currentCurrency.subscribe(value => currentCurrencyValue = value)()
  
  const currencyToUse = currency || currentCurrencyValue
  return formatPriceUtil(amount, currencyToUse) // Convert to cents
}

/**
 * Get currency symbol for display
 */
export function getCurrencySymbol(currency?: string): string {
  let currentCurrencyValue = 'USD'
  currentCurrency.subscribe(value => currentCurrencyValue = value)()
  
  const currencyToUse = currency || currentCurrencyValue
  
  const symbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    ILS: '₪',
    JPY: '¥',
    CAD: 'C$',
    AUD: 'A$'
  }
  
  return symbols[currencyToUse.toUpperCase()] || currencyToUse.toUpperCase()
}
