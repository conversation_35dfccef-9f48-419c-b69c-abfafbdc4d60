import { defineWidgetConfig } from "@medusajs/admin-sdk"
import { useState, useEffect } from "react"
import {
  AdminProduct,
  AdminProductVariant,
  DetailWidgetProps,
} from "@medusajs/framework/types"
import {
  Container,
  Heading,
  Button,
  Label,
  Text,
} from "@medusajs/ui"

type DigitalProduct = {
  id: string
  name: string
  medias: DigitalProductMedia[]
  product_variant?: {
    id: string
    product_id: string
  }
}

type DigitalProductMedia = {
  id: string
  type: "preview" | "main"
  url?: string
  fileId?: string
  mimeType?: string
}

export const DigitalProductWidget = ({ data: product }: DetailWidgetProps<AdminProduct>) => {
  const [digitalProduct, setDigitalProduct] = useState<DigitalProduct | null>(null)
  const [loading, setLoading] = useState(true)

  // Early return if product is not available
  if (!product) {
    return (
      <Container className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Heading level="h2">📚 Digital Product</Heading>
        </div>
        <Text className="text-gray-500">Product information not available.</Text>
      </Container>
    )
  }

  // Load existing digital product linked to this product's variants
  useEffect(() => {
    const fetchDigitalProduct = async () => {
      // Check if product exists
      if (!product) {
        console.log('No product provided to widget')
        setLoading(false)
        return
      }

      // First, fetch the product with variants if not already loaded
      let productWithVariants = product
      if (!product.variants || product.variants.length === 0) {
        try {
          const productResponse = await fetch(`/admin/products/${product.id}?fields=*variants`, {
            credentials: 'include',
          })
          if (productResponse.ok) {
            const { product: fetchedProduct } = await productResponse.json()
            productWithVariants = fetchedProduct
          }
        } catch (error) {
          console.error('Error fetching product variants:', error)
        }
      }

      if (!productWithVariants.variants || productWithVariants.variants.length === 0) {
        console.log('Product has no variants')
        setLoading(false)
        return
      }

      try {
        // Use the MedusaJS v2 query.graph approach to get digital products with linked product variants
        // The API route already includes "product_variant.*" in the fields, so we get the linked data
        const variantIds = productWithVariants.variants?.map((v: AdminProductVariant) => v.id) || []

        const response = await fetch(`/admin/digital-products?fields=*,*medias,*product_variant&limit=100`, {
          credentials: 'include',
        })

        if (!response.ok) {
          console.error('Failed to fetch digital products')
          setLoading(false)
          return
        }

        const { digital_products } = await response.json()

        // Find digital product linked to any of this product's variants
        // The product_variant field should be populated by the module link
        const linkedDigitalProduct = digital_products.find((dp: DigitalProduct) => {
          return dp.product_variant && variantIds.includes(dp.product_variant.id)
        })

        console.log('Product variants:', variantIds)
        console.log('Digital products:', digital_products)
        console.log('Found linked digital product:', linkedDigitalProduct)

        setDigitalProduct(linkedDigitalProduct || null)
      } catch (error) {
        console.error('Error fetching digital product:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDigitalProduct()
  }, [product?.id])

  if (loading) {
    return (
      <Container className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Heading level="h2">📚 Digital Product</Heading>
        </div>
        <Text className="text-gray-500">Loading digital product information...</Text>
      </Container>
    )
  }

  if (!digitalProduct) {
    return (
      <Container className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Heading level="h2">📚 Digital Product</Heading>
          <Button
            variant="secondary"
            size="small"
            onClick={() => window.open('/app/digital-products', '_blank')}
          >
            Create Digital Product
          </Button>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <Text className="text-gray-600">
            This product is not linked to any digital content. Create a digital product to add downloadable content.
          </Text>
        </div>
      </Container>
    )
  }

  return (
    <Container className="p-6">
      <div className="flex items-center justify-between mb-4">
        <Heading level="h2">📚 Digital Product</Heading>
        <Button
          variant="secondary"
          size="small"
          onClick={() => window.open('/app/digital-products', '_blank')}
        >
          Manage Digital Products
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium text-gray-900">Digital Product Name</Label>
          <Text className="text-sm text-gray-600">{digitalProduct.name}</Text>
        </div>

        {digitalProduct.medias && digitalProduct.medias.length > 0 && (
          <div>
            <Label className="text-sm font-medium text-gray-900">Digital Content</Label>
            <div className="mt-2 space-y-2">
              {digitalProduct.medias.map((media) => (
                <div key={media.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        media.type === 'preview'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {media.type === 'preview' ? '👁️ Preview' : '📚 Main Content'}
                      </span>
                    </div>
                    {media.url && (
                      <Text className="text-xs text-gray-500 truncate">
                        URL: {media.url}
                      </Text>
                    )}
                    {media.fileId && (
                      <Text className="text-xs text-gray-500">
                        File ID: {media.fileId}
                      </Text>
                    )}
                    {media.mimeType && (
                      <Text className="text-xs text-gray-500">
                        Type: {media.mimeType}
                      </Text>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="pt-4 border-t bg-blue-50 p-3 rounded-lg">
          <Text className="text-xs text-blue-800">
            💡 <strong>Digital Fulfillment:</strong> When customers purchase this product, they will automatically receive email notifications with download links for the digital content above.
          </Text>
        </div>
      </div>
    </Container>
  )
}

export const config = defineWidgetConfig({
  zone: "product.details.after",
})

export default DigitalProductWidget
