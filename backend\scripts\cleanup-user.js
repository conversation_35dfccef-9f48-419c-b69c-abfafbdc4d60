#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to clean up a user and their related data
 * Usage: node scripts/cleanup-user.js <email>
 * Example: node scripts/cleanup-user.js <EMAIL>
 */

require('dotenv').config()
const { createMedusaContainer } = require('@medusajs/framework')
const { ContainerRegistrationKeys } = require('@medusajs/framework/utils')

async function cleanupUser(email) {
  if (!email) {
    console.error('❌ Email is required')
    console.log('Usage: node scripts/cleanup-user.js <email>')
    process.exit(1)
  }

  try {
    console.log(`🧹 Cleaning up user: ${email}`)
    
    const container = createMedusaContainer()
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    
    // Find user
    const { data: users } = await query.graph({
      entity: "user",
      fields: ["id", "email"],
      filters: { email }
    })
    
    if (users && users.length > 0) {
      const user = users[0]
      console.log(`👤 Found user: ${user.email} (ID: ${user.id})`)
      
      // Delete user (this should cascade to related records)
      try {
        await query.graph({
          entity: "user",
          filters: { id: user.id }
        }, "delete")
        console.log(`✅ Deleted user: ${user.email}`)
      } catch (error) {
        console.error(`❌ Error deleting user:`, error.message)
      }
    } else {
      console.log(`👤 No user found with email: ${email}`)
    }
    
    // Find and delete invites
    const { data: invites } = await query.graph({
      entity: "invite",
      fields: ["id", "email", "accepted"],
      filters: { email }
    })
    
    if (invites && invites.length > 0) {
      for (const invite of invites) {
        console.log(`📨 Found invite: ${invite.email} (ID: ${invite.id}, Accepted: ${invite.accepted})`)
        
        try {
          await query.graph({
            entity: "invite",
            filters: { id: invite.id }
          }, "delete")
          console.log(`✅ Deleted invite: ${invite.id}`)
        } catch (error) {
          console.error(`❌ Error deleting invite:`, error.message)
        }
      }
    } else {
      console.log(`📨 No invites found with email: ${email}`)
    }
    
    // Try to find and delete auth identities
    try {
      const { data: identities } = await query.graph({
        entity: "auth_identity",
        fields: ["id", "provider_identities"]
      })
      
      if (identities && identities.length > 0) {
        for (const identity of identities) {
          // Check if any provider identity matches the email
          const hasMatchingEmail = identity.provider_identities?.some(
            pi => pi.entity_id === email
          )
          
          if (hasMatchingEmail) {
            console.log(`🔐 Found auth identity: ${identity.id}`)
            
            try {
              await query.graph({
                entity: "auth_identity",
                filters: { id: identity.id }
              }, "delete")
              console.log(`✅ Deleted auth identity: ${identity.id}`)
            } catch (error) {
              console.error(`❌ Error deleting auth identity:`, error.message)
            }
          }
        }
      }
    } catch (error) {
      console.log(`⚠️ Could not check auth identities:`, error.message)
    }
    
    console.log(`\n🎉 Cleanup completed for: ${email}`)
    console.log(`💡 You can now create a new invite for this email address`)
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error)
  }
}

// Get email from command line
const email = process.argv[2]
cleanupUser(email)
