<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { authStore, isAuthenticated, currentCustomer } from '$lib/stores/auth'
  import { apiClient } from '$lib/api/client'
  import { formatPriceWithCurrency } from '$lib/stores/currency'
  import { _ } from '$lib/i18n'
  import { getCurrentLocale, isRTL } from '$lib/i18n'

  let currentLocale = 'en'
  let rtl = false
  
  // Loading states
  let isLoadingOrders = false
  let isLoadingSubscriptions = false
  let ordersError = ''
  let subscriptionsError = ''
  
  // Data
  let orders: any[] = []
  let subscriptions: any[] = []

  $: currentLocale = getCurrentLocale()
  $: rtl = isRTL(currentLocale)
  
  // Redirect to home if user logs out
  $: if (!$isAuthenticated && typeof window !== 'undefined') {
    goto('/')
  }

  onMount(async () => {
    // Initialize auth state from localStorage with token validation
    await authStore.initialize()

    // Redirect to home if not authenticated
    if (!$isAuthenticated) {
      goto('/')
      return
    }

    // Load orders and subscriptions
    loadOrders()
    loadSubscriptions()
  })

  async function loadOrders() {
    if (!$currentCustomer) return

    try {
      isLoadingOrders = true
      ordersError = ''

      const token = localStorage.getItem('auth_token')
      if (!token) {
        ordersError = 'Authentication token not found. Please sign in again.'
        return
      }

      const response = await apiClient.getCustomerOrders(token)
      if (response.data?.orders) {
        orders = response.data.orders
        console.log('📦 Loaded orders:', orders)
      } else if (response.error) {
        ordersError = response.error
      }
    } catch (error) {
      console.error('Failed to load orders:', error)
      ordersError = 'Failed to load orders. Please try again.'
    } finally {
      isLoadingOrders = false
    }
  }

  async function loadSubscriptions() {
    if (!$currentCustomer) return

    try {
      isLoadingSubscriptions = true
      subscriptionsError = ''

      const token = localStorage.getItem('auth_token')
      if (!token) {
        subscriptionsError = 'Authentication token not found. Please sign in again.'
        return
      }

      // Note: This would need to be implemented based on your subscription system
      // For now, we'll filter orders for subscription products
      const response = await apiClient.getCustomerOrders(token)
      if (response.data?.orders) {
        // Filter for subscription-type products
        subscriptions = response.data.orders.filter((order: any) => 
          order.items?.some((item: any) => 
            item.product_type?.toLowerCase() === 'subscription'
          )
        )
        console.log('⭐ Loaded subscriptions:', subscriptions)
      } else if (response.error) {
        subscriptionsError = response.error
      }
    } catch (error) {
      console.error('Failed to load subscriptions:', error)
      subscriptionsError = 'Failed to load subscriptions. Please try again.'
    } finally {
      isLoadingSubscriptions = false
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString(currentLocale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  function getOrderStatus(status: string) {
    const statusMap: Record<string, string> = {
      'pending': 'Pending',
      'completed': 'Completed',
      'canceled': 'Canceled',
      'requires_action': 'Requires Action'
    }
    return statusMap[status] || status
  }

  function getOrderStatusColor(status: string) {
    const colorMap: Record<string, string> = {
      'pending': '#f59e0b',
      'completed': '#10b981',
      'canceled': '#ef4444',
      'requires_action': '#f59e0b'
    }
    return colorMap[status] || '#6b7280'
  }
</script>

<svelte:head>
  <title>{$_('orders.page_title')} - Hebrew Book Store</title>
  <meta name="description" content={$_('orders.page_description')} />
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    {#if $isAuthenticated && $currentCustomer}
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="font-book-title text-3xl md:text-4xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('orders.my_orders')}
        </h1>
        <p class="font-book-text text-lg" style="color: var(--color-text-secondary);">
          {$_('orders.view_purchase_history')}
        </p>
      </div>

      <!-- Navigation Tabs -->
      <div class="mb-8">
        <div class="border-b" style="border-color: var(--color-border);">
          <nav class="-mb-px flex space-x-8">
            <button class="py-2 px-1 border-b-2 font-book-text text-sm font-medium transition-colors"
                    style="border-color: var(--color-primary); color: var(--color-primary);">
              {$_('orders.purchase_history')}
            </button>
            <button class="py-2 px-1 border-b-2 border-transparent font-book-text text-sm font-medium transition-colors hover:border-gray-300"
                    style="color: var(--color-text-secondary);">
              {$_('orders.active_subscriptions')}
            </button>
          </nav>
        </div>
      </div>

      <!-- Purchase History Section -->
      <div class="mb-12">
        <h2 class="font-book-title text-2xl font-bold mb-6" style="color: var(--color-text-primary);">
          {$_('orders.purchase_history')}
        </h2>

        {#if isLoadingOrders}
          <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
            <p class="font-book-text" style="color: var(--color-text-secondary);">
              {$_('orders.loading_orders')}
            </p>
          </div>
        {:else if ordersError}
          <div class="card-classic p-6 text-center">
            <div class="text-4xl mb-4">❌</div>
            <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
              {$_('orders.error_loading_orders')}
            </h3>
            <p class="font-book-text text-sm mb-4" style="color: var(--color-text-secondary);">
              {ordersError}
            </p>
            <button on:click={loadOrders} class="btn-classic-small">
              {$_('orders.retry')}
            </button>
          </div>
        {:else if orders.length === 0}
          <div class="card-classic p-8 text-center">
            <div class="text-6xl mb-4">📦</div>
            <h3 class="font-book-title text-xl font-semibold mb-2" style="color: var(--color-text-primary);">
              {$_('orders.no_orders')}
            </h3>
            <p class="font-book-text mb-6" style="color: var(--color-text-secondary);">
              {$_('orders.no_orders_description')}
            </p>
            <a href="/catalog" class="btn-classic">
              {$_('orders.start_shopping')}
            </a>
          </div>
        {:else}
          <div class="space-y-4">
            {#each orders as order (order.id)}
              <div class="card-classic p-6">
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <h3 class="font-book-title text-lg font-semibold" style="color: var(--color-text-primary);">
                      {$_('orders.order')} #{order.display_id || order.id.slice(-8)}
                    </h3>
                    <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
                      {formatDate(order.created_at)}
                    </p>
                  </div>
                  <div class="text-right">
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                         style="background-color: {getOrderStatusColor(order.status)}20; color: {getOrderStatusColor(order.status)};">
                      {getOrderStatus(order.status)}
                    </div>
                    <p class="font-book-title text-lg font-bold mt-1" style="color: var(--color-text-primary);">
                      {formatPriceWithCurrency(order.total)}
                    </p>
                  </div>
                </div>

                {#if order.items && order.items.length > 0}
                  <div class="border-t pt-4" style="border-color: var(--color-border);">
                    <div class="space-y-2">
                      {#each order.items as item (item.id)}
                        <div class="flex items-center justify-between">
                          <div class="flex items-center space-x-3">
                            <div class="text-2xl">
                              {#if item.product_type?.toLowerCase() === 'book'}
                                📚
                              {:else if item.product_type?.toLowerCase() === 'chapter'}
                                📖
                              {:else if item.product_type?.toLowerCase() === 'subscription'}
                                ⭐
                              {:else}
                                📦
                              {/if}
                            </div>
                            <div>
                              <p class="font-book-text font-medium" style="color: var(--color-text-primary);">
                                {item.product?.title || item.title}
                              </p>
                              <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
                                {$_('orders.quantity')}: {item.quantity}
                              </p>
                            </div>
                          </div>
                          <p class="font-book-text font-medium" style="color: var(--color-text-primary);">
                            {formatPriceWithCurrency(item.unit_price)}
                          </p>
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}
              </div>
            {/each}
          </div>
        {/if}
      </div>

      <!-- Active Subscriptions Section -->
      <div class="mb-12">
        <h2 class="font-book-title text-2xl font-bold mb-6" style="color: var(--color-text-primary);">
          {$_('orders.active_subscriptions')}
        </h2>

        {#if isLoadingSubscriptions}
          <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
            <p class="font-book-text" style="color: var(--color-text-secondary);">
              {$_('orders.loading_orders')}
            </p>
          </div>
        {:else if subscriptionsError}
          <div class="card-classic p-6 text-center">
            <div class="text-4xl mb-4">❌</div>
            <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
              {$_('orders.error_loading_orders')}
            </h3>
            <p class="font-book-text text-sm mb-4" style="color: var(--color-text-secondary);">
              {subscriptionsError}
            </p>
            <button on:click={loadSubscriptions} class="btn-classic-small">
              {$_('orders.retry')}
            </button>
          </div>
        {:else if subscriptions.length === 0}
          <div class="card-classic p-8 text-center">
            <div class="text-6xl mb-4">⭐</div>
            <h3 class="font-book-title text-xl font-semibold mb-2" style="color: var(--color-text-primary);">
              {$_('orders.no_subscriptions')}
            </h3>
            <p class="font-book-text mb-6" style="color: var(--color-text-secondary);">
              {$_('orders.no_subscriptions_description')}
            </p>
            <a href="/catalog?type=subscription" class="btn-classic">
              {$_('orders.browse_subscriptions')}
            </a>
          </div>
        {:else}
          <div class="space-y-4">
            {#each subscriptions as subscription (subscription.id)}
              <div class="card-classic p-6">
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <h3 class="font-book-title text-lg font-semibold" style="color: var(--color-text-primary);">
                      {$_('orders.subscription')} #{subscription.display_id || subscription.id.slice(-8)}
                    </h3>
                    <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
                      {$_('orders.started')}: {formatDate(subscription.created_at)}
                    </p>
                  </div>
                  <div class="text-right">
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                         style="background-color: #10b98120; color: #10b981;">
                      {$_('orders.active')}
                    </div>
                    <p class="font-book-title text-lg font-bold mt-1" style="color: var(--color-text-primary);">
                      {formatPriceWithCurrency(subscription.total)}
                    </p>
                  </div>
                </div>

                {#if subscription.items && subscription.items.length > 0}
                  <div class="border-t pt-4" style="border-color: var(--color-border);">
                    <div class="space-y-2">
                      {#each subscription.items as item (item.id)}
                        <div class="flex items-center justify-between">
                          <div class="flex items-center space-x-3">
                            <div class="text-2xl">⭐</div>
                            <div>
                              <p class="font-book-text font-medium" style="color: var(--color-text-primary);">
                                {item.product?.title || item.title}
                              </p>
                              <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
                                {$_('orders.subscription_type')}
                              </p>
                            </div>
                          </div>
                          <p class="font-book-text font-medium" style="color: var(--color-text-primary);">
                            {formatPriceWithCurrency(item.unit_price)}
                          </p>
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}
              </div>
            {/each}
          </div>
        {/if}
      </div>

    {:else}
      <!-- Not Authenticated -->
      <div class="text-center py-16">
        <div class="text-6xl mb-6">🔒</div>
        <h1 class="font-book-title text-2xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('orders.access_required')}
        </h1>
        <p class="font-book-text text-lg mb-8" style="color: var(--color-text-secondary);">
          {$_('orders.please_sign_in')}
        </p>
        <a href="/" class="btn-classic inline-block">
          {$_('orders.go_home')}
        </a>
      </div>
    {/if}
  </div>
</div>
