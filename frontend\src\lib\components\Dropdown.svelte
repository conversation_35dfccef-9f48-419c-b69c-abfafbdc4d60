<script lang="ts">
	import { createEventDispatcher } from 'svelte'
	import { _ } from '$lib/i18n'

	export let options: { value: string; label: string }[] = []
	export let selected: string = ''
	export let placeholder: string = 'Select option'
	export let multiple: boolean = false
	export let disabled: boolean = false

	const dispatch = createEventDispatcher()

	let isOpen = false
	let dropdownElement: HTMLDivElement

	function toggleDropdown() {
		if (!disabled) {
			isOpen = !isOpen
		}
	}

	function selectOption(value: string) {
		if (multiple) {
			// For multiple selection, we'll handle this differently
			// For now, just single selection
			selected = value
			dispatch('change', value)
		} else {
			selected = value
			dispatch('change', value)
			isOpen = false
		}
	}

	function closeDropdown() {
		isOpen = false
	}

	// Close dropdown when clicking outside
	function handleClickOutside(event: MouseEvent) {
		if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
			closeDropdown()
		}
	}

	// Get display text for selected option
	$: selectedOption = options.find(opt => opt.value === selected)
	$: displayText = selectedOption ? selectedOption.label : placeholder
</script>

<svelte:window on:click={handleClickOutside} />

<div class="relative" bind:this={dropdownElement}>
	<!-- Dropdown Button -->
	<button
		type="button"
		class="relative w-full cursor-pointer rounded-lg border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm"
		class:opacity-50={disabled}
		class:cursor-not-allowed={disabled}
		on:click={toggleDropdown}
		aria-haspopup="listbox"
		aria-expanded={isOpen}
	>
		<span class="block truncate font-book-text text-sm" style="color: var(--color-text-primary);">
			{displayText}
		</span>
		<span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
			<svg 
				class="h-5 w-5 text-gray-400 transition-transform duration-200"
				class:rotate-180={isOpen}
				fill="none" 
				stroke="currentColor" 
				viewBox="0 0 24 24"
			>
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
			</svg>
		</span>
	</button>

	<!-- Dropdown Options -->
	{#if isOpen}
		<div class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
			{#each options as option (option.value)}
				<button
					type="button"
					class="relative w-full cursor-pointer select-none py-2 pl-3 pr-9 text-left hover:bg-blue-50 focus:bg-blue-50 focus:outline-none"
					class:bg-blue-100={selected === option.value}
					class:text-blue-600={selected === option.value}
					class:text-gray-900={selected !== option.value}
					on:click={() => selectOption(option.value)}
				>
					<span class="block truncate font-book-text text-sm">
						{option.label}
					</span>
					{#if selected === option.value}
						<span class="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
							<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
							</svg>
						</span>
					{/if}
				</button>
			{/each}
		</div>
	{/if}
</div>
