<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { cartActions } from '$lib/stores/cart'
  import { formatPriceWithCurrency } from '$lib/stores/currency'

  export let cart: any
  export let onComplete: (result: { success: boolean, order?: any, error?: string }) => void
  export let onBack: () => void

  let stripeLoaded = false
  let stripe: any = null
  let elements: any = null
  let cardElement: any = null
  let isProcessing = false
  let paymentError = ''
  let clientSecret = ''
  let elementsInitialized = false

  // Get Stripe publishable key from environment
  const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY

  // Reactive statement to initialize elements when everything is ready
  $: if (stripeLoaded && clientSecret && !elementsInitialized && typeof document !== 'undefined') {
    // Check if the DOM element exists
    const cardElementDiv = document.getElementById('card-element')
    if (cardElementDiv) {
      console.log('🔄 DOM element found, initializing Stripe Elements')
      initializeElements()
      elementsInitialized = true
    }
  }

  onMount(async () => {
    await loadStripe()
    extractClientSecret()
  })

  async function loadStripe() {
    try {
      // Dynamically import Stripe
      const { loadStripe } = await import('@stripe/stripe-js')
      
      if (!stripePublishableKey) {
        throw new Error('Stripe publishable key not found')
      }

      stripe = await loadStripe(stripePublishableKey)
      
      if (!stripe) {
        throw new Error('Failed to load Stripe')
      }

      stripeLoaded = true
      console.log('✅ /* Stripe loaded successfully */')
    } catch (error) {
      console.error('Failed to load Stripe:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  function extractClientSecret() {
    // Extract client secret from payment session based on the console output structure
    console.log('🔍 Full cart object:', cart)
    console.log('🔍 Payment collection:', cart?.payment_collection)
    
    const paymentSession = cart?.payment_collection?.payment_sessions?.[0]
    console.log('🔍 First payment session:', paymentSession)
    
    // Based on console output: paymentSession.data.client_secret
    if (paymentSession?.data?.client_secret) {
      clientSecret = paymentSession.data.client_secret
      console.log('✅ Client secret found:', clientSecret.substring(0, 20) + '...')
    } else {
      console.warn('⚠️ No client secret found in payment session')
      console.log('🔍 Payment session data:', paymentSession?.data)
      paymentError = $_('checkout.no_client_secret')
    }
  }

  function initializeElements() {
    if (!stripe) {
      console.warn('⚠️ Stripe not loaded yet')
      return
    }

    if (!clientSecret) {
      console.warn('⚠️ No client secret available')
      // Try to extract client secret again
      extractClientSecret()
      if (!clientSecret) {
        paymentError = $_('checkout.no_client_secret')
        return
      }
    }

    // Check if DOM element exists
    const cardElementDiv = document.getElementById('card-element')
    if (!cardElementDiv) {
      console.warn('⚠️ Card element not found in DOM')
      return
    }

    console.log('🔄 Initializing Stripe Elements with client secret')

    try {
      // When using clientSecret, we should use Payment Element, not Card Element
      elements = stripe.elements({
        clientSecret: clientSecret,
        appearance: {
          theme: 'stripe',
          variables: {
            colorPrimary: '#3b82f6',
            colorBackground: '#ffffff',
            colorText: '#1f2937',
            colorDanger: '#ef4444',
            fontFamily: 'system-ui, sans-serif',
            spacingUnit: '4px',
            borderRadius: '8px'
          }
        }
      })

      // Create payment element instead of card element when using clientSecret
      cardElement = elements.create('payment', {
        layout: 'tabs'
      })

      // Mount payment element
      cardElement.mount('#card-element')

      // Listen for changes
      cardElement.on('change', (event: any) => {
        if (event.error) {
          paymentError = event.error.message
        } else {
          paymentError = ''
        }
      })

      console.log('✅ Stripe Payment Element initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Stripe Elements:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  async function handlePayment() {
    if (!stripe || !cardElement || !cart || !clientSecret) {
      paymentError = $_('checkout.payment_not_ready')
      return
    }

    try {
      isProcessing = true
      paymentError = ''

      console.log('🔄 Processing payment with Stripe Payment Element')

      // When using Payment Element, we use confirmPayment instead of confirmCardPayment
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/checkout/success`,
        },
        redirect: 'if_required'
      })

      if (error) {
        console.error('❌ Stripe payment error:', error)
        paymentError = error.message || $_('checkout.payment_failed')
        onComplete({ success: false, error: paymentError })
        return
      }

      if (paymentIntent.status === 'succeeded') {
        console.log('✅ Payment succeeded, completing cart')

        // Complete the cart
        const completeResponse = await apiClient.completeCart(cart.id)
        
        if (completeResponse.data?.type === 'order' && completeResponse.data.order) {
          // Order placed successfully
          console.log('✅ Order placed successfully:', completeResponse.data.order.id)
          cartActions.initialize() // Refresh cart
          onComplete({ 
            success: true, 
            order: completeResponse.data.order 
          })
        } else {
          // Cart completion failed
          console.error('❌ Cart completion failed')
          const errorMsg = completeResponse.data?.error || $_('checkout.order_completion_failed')
          paymentError = errorMsg
          onComplete({ success: false, error: errorMsg })
        }
      } else {
        console.warn('⚠️ Payment not completed:', paymentIntent.status)
        paymentError = $_('checkout.payment_incomplete')
        onComplete({ success: false, error: paymentError })
      }
    } catch (error) {
      console.error('❌ Payment processing error:', error)
      paymentError = $_('checkout.payment_processing_error')
      onComplete({ success: false, error: paymentError })
    } finally {
      isProcessing = false
    }
  }
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="text-center">
    <h3 class="text-xl font-bold mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-title);">
      {$_('checkout.payment_details')}
    </h3>
    <p class="text-sm" style="color: var(--color-text-secondary); font-family: var(--font-book-text);">
      {$_('checkout.payment_step_description')}
    </p>
  </div>

  <!-- Error Display -->
  {#if paymentError}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <span class="text-red-500 mr-2">⚠️</span>
        <span class="text-red-700 font-book-text">{paymentError}</span>
      </div>
    </div>
  {/if}

  <!-- Loading State -->
  {#if !stripeLoaded}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p class="text-sm" style="color: var(--color-text-secondary); font-family: var(--font-book-text);">
        {$_('checkout.loading_payment_system')}
      </p>
    </div>
  {/if}

  {#if stripeLoaded && clientSecret}
    <!-- Payment Form -->
    <div class="space-y-4">
      <div>
        <h4 class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.card_details')} *
        </h4>
        <div class="border border-gray-300 rounded-lg p-4 bg-white">
          <div id="card-element">
            <!-- Stripe Elements will create form elements here -->
          </div>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
        <span class="text-green-500 mr-2">🔒</span>
        <span class="font-book-text">
          {$_('checkout.secure_payment_notice')}
        </span>
      </div>

    <!-- Form Actions -->
    <div class="flex justify-between pt-4">
      <button
        type="button"
        on:click={onBack}
        disabled={isProcessing}
        class="btn-classic-outline"
        class:opacity-50={isProcessing}
      >
        ← {$_('checkout.back')}
      </button>

      <button
        type="button"
        on:click={handlePayment}
        disabled={isProcessing || !stripeLoaded || !!paymentError}
        class="btn-classic"
        class:opacity-50={isProcessing || !stripeLoaded || !!paymentError}
      >
        {#if isProcessing}
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {$_('checkout.processing_payment')}
          </div>
        {:else}
          🔒 {$_('checkout.place_order')} - {cart.total ? formatPriceWithCurrency(cart.total) : cart.total_amount ? 
            formatPriceWithCurrency(cart.total_amount) : '0.00'}
        {/if}
      </button>
    </div>
    </div>
  {/if}
</div>
