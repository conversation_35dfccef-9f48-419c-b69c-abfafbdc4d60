import { 
  AdminCreateProduct,
} from "@medusajs/medusa/api/admin/products/validators"
import { z } from "zod"
import { MediaType } from "../modules/digital-product/types"

export const createDigitalProductsSchema = z.object({
  name: z.string(),
  product_variant_id: z.string().optional(),
  medias: z.array(z.object({
    type: z.nativeEnum(MediaType),
    file_id: z.string().optional(),
    mime_type: z.string().optional(),
    url: z.string().optional(),
    useDirectUrl: z.boolean().optional(),
  })),
  product: AdminCreateProduct().optional(),
})