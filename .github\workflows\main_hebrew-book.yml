# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - hebrew-book

on:
  push:
    branches:
      - main
    paths:
      - 'backend/**'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'yarn'
          cache-dependency-path: backend/yarn.lock

      - name: Install dependencies
        run: |
          cd backend
          yarn install --frozen-lockfile

      - name: Build application
        run: |
          cd backend
          echo "🔨 Starting build process..."

          # Ensure clean build environment
          rm -rf .medusa/server .medusa/admin .cache

          # Verify tsconfig.json exists
          if [ ! -f "tsconfig.json" ]; then
            echo "❌ tsconfig.json not found!"
            exit 1
          fi

          echo "✅ tsconfig.json found, starting build..."
          yarn build

          echo "🔍 POST-BUILD VERIFICATION:"
          echo "📂 Backend directory after build:"
          ls -la

          echo "🔍 Looking for build outputs:"
          echo "📁 .medusa directory exists: $(test -d .medusa && echo 'YES' || echo 'NO')"
          if [ -d ".medusa" ]; then
            echo "📁 .medusa contents:"
            ls -la .medusa/
            if [ -d ".medusa/server" ]; then
              echo "📁 .medusa/server contents:"
              ls -la .medusa/server/
            fi
          fi

          echo "📁 dist directory exists: $(test -d dist && echo 'YES' || echo 'NO')"
          if [ -d "dist" ]; then
            echo "📁 dist contents:"
            ls -la dist/
          fi

          echo "📁 build directory exists: $(test -d build && echo 'YES' || echo 'NO')"
          if [ -d "build" ]; then
            echo "📁 build contents:"
            ls -la build/
          fi

      - name: Prepare deployment package
        run: |
          echo "🔍 Current directory: $(pwd)"
          echo "📁 Root directory contents:"
          ls -la

          echo "📂 Backend directory contents:"
          ls -la backend/

          echo "🔍 Looking for .medusa directory in backend:"
          ls -la backend/.medusa/ || echo "❌ No .medusa directory found in backend/"

          echo "🔨 Creating deployment directory..."
          mkdir -p deploy

          echo "📦 Copying files from backend to deploy..."
          cd backend

          # Copy built files and necessary runtime files
          if [ -d ".medusa" ]; then
            echo "✅ Found .medusa directory, copying..."
            cp -r .medusa ../deploy/
            echo "📁 .medusa contents:"
            ls -la .medusa/

          # Copy essential runtime files to deploy root
          cp startup.sh ../deploy/ || echo "⚠️  No startup.sh found"
          cp medusa-config.ts ../deploy/ || echo "⚠️  No medusa-config.ts found"
          cp package.json ../deploy/ || echo "⚠️  No package.json found"
          cp yarn.lock ../deploy/ || echo "⚠️  No yarn.lock found"

          # Make startup.sh executable
          chmod +x ../deploy/startup.sh || echo "⚠️  Could not make startup.sh executable"

          cd ..
          echo "📋 Final deployment package contents:"
          ls -la deploy/

          echo "📋 Deployment package size:"
          du -sh deploy/

      - name: Debug deployment package before upload
        run: |
          echo "🔍 FINAL VERIFICATION BEFORE UPLOAD:"
          echo "📂 Current working directory: $(pwd)"
          echo "📂 Full path to current directory: $(realpath .)"
          echo "📁 All items in current directory:"
          ls -la
          echo "📁 Deploy directory exists: $(test -d deploy && echo 'YES' || echo 'NO')"
          echo "📁 ./deploy directory exists: $(test -d ./deploy && echo 'YES' || echo 'NO')"
          echo "📂 Full path to deploy directory: $(realpath ./deploy 2>/dev/null || echo 'DOES NOT EXIST')"

          if [ -d "deploy" ]; then
            echo "📋 Deploy directory contents (detailed):"
            ls -la deploy/

            echo "🔍 Checking for .medusa in deploy:"
            if [ -d "deploy/.medusa" ]; then
              echo "✅ .medusa directory found in deploy!"
              echo "📁 .medusa contents:"
              ls -la deploy/.medusa/
              if [ -d "deploy/.medusa/server" ]; then
                echo "✅ .medusa/server directory found!"
                echo "📁 .medusa/server contents:"
                ls -la deploy/.medusa/server/
              else
                echo "❌ No .medusa/server directory found"
              fi
            else
              echo "❌ No .medusa directory found in deploy package"
            fi

            echo "📊 Deploy package size breakdown:"
            du -sh deploy/* 2>/dev/null || echo "No files to measure"

            echo "📦 Total deploy package size:"
            du -sh deploy/
          else
            echo "❌ Deploy directory does not exist!"
            echo "📁 Current directory contents:"
            ls -la
          fi

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: ${{ github.workspace }}/deploy
          if-no-files-found: error
          include-hidden-files: true

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Verify downloaded artifact
        run: |
          echo "🔍 Downloaded artifact contents:"
          ls -la
          echo "📁 Checking for .medusa directory:"
          ls -la .medusa/ || echo "❌ No .medusa directory in artifact"
          echo "📋 Total artifact size:"
          du -sh .
          echo "🔍 Node.js version in deployment:"
          node --version

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_3393D489ACA148AEA5AEBE7711560F91 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_F9EA7C3648F040778F81024500DA12B7 }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_BE9ED13D58B342E5A0D767CF12E0FB2C }}

      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'hebrew-book'
          slot-name: 'Production'
          package: '.'
          startup-command: 'chmod +x startup.sh && ./startup.sh'
