# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - hebrew-book

on:
  push:
    branches:
      - main
    paths:
      - 'backend/**'
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'yarn'
          cache-dependency-path: backend/yarn.lock

      - name: Install dependencies
        run: |
          cd backend
          yarn install --frozen-lockfile

      - name: Build application
        run: |
          cd backend
          echo "🔨 Starting build process..."
          yarn build

          echo "🔍 POST-BUILD VERIFICATION:"
          echo "📂 Backend directory after build:"
          ls -la

          echo "🔍 Looking for build outputs:"
          echo "📁 .medusa directory exists: $(test -d .medusa && echo 'YES' || echo 'NO')"
          if [ -d ".medusa" ]; then
            echo "📁 .medusa contents:"
            ls -la .medusa/
            if [ -d ".medusa/server" ]; then
              echo "📁 .medusa/server contents:"
              ls -la .medusa/server/
            fi
          fi

          echo "📁 dist directory exists: $(test -d dist && echo 'YES' || echo 'NO')"
          if [ -d "dist" ]; then
            echo "📁 dist contents:"
            ls -la dist/
          fi

          echo "📁 build directory exists: $(test -d build && echo 'YES' || echo 'NO')"
          if [ -d "build" ]; then
            echo "📁 build contents:"
            ls -la build/
          fi

      - name: Prepare deployment package
        run: |
          echo "🔍 Current directory: $(pwd)"
          echo "📁 Root directory contents:"
          ls -la

          echo "📂 Backend directory contents:"
          ls -la backend/

          echo "🔍 Looking for .medusa directory in backend:"
          ls -la backend/.medusa/ || echo "❌ No .medusa directory found in backend/"

          echo "🔨 Creating clean deployment directory..."
          mkdir -p deploy

          echo "📦 Creating production-only deployment package..."
          cd backend

          # Copy ONLY the built application (.medusa/server contains everything needed)
          if [ -d ".medusa" ]; then
            echo "✅ Found .medusa directory, copying built application..."
            cp -r .medusa ../deploy/
            echo "📁 .medusa contents:"
            ls -la .medusa/

            # Pre-install production dependencies in the built server
            if [ -d ".medusa/server" ]; then
              echo "✅ Found .medusa/server directory"
              echo "📁 Built server contents:"
              ls -la .medusa/server/

              # Check if package.json exists and install production dependencies
              if [ -f ".medusa/server/package.json" ]; then
                echo "✅ Found package.json in .medusa/server"
                echo "📦 Pre-installing production dependencies..."
                cd .medusa/server
                yarn install --production --frozen-lockfile --network-timeout 300000
                echo "✅ Production dependencies installed"
                cd ../..
              else
                echo "⚠️  No package.json in .medusa/server - will use fallback"
              fi
            else
              echo "❌ No .medusa/server directory found after build"
              exit 1
            fi
          else
            echo "❌ No .medusa directory found after build"
            echo "⚠️  Build process failed - cannot deploy"
            exit 1
          fi

          # Copy ONLY essential runtime files (minimal set)
          echo "📋 Copying minimal runtime files..."
          cp startup.sh ../deploy/ || echo "⚠️  No startup.sh found"
          cp medusa-config.ts ../deploy/ || echo "⚠️  No medusa-config.ts found"

          # Copy package.json and yarn.lock ONLY as fallback (should not be needed)
          cp package.json ../deploy/ || echo "⚠️  No package.json found"
          cp yarn.lock ../deploy/ || echo "⚠️  No yarn.lock found"

          # Make startup.sh executable
          chmod +x ../deploy/startup.sh || echo "⚠️  Could not make startup.sh executable"

          echo "📋 PRODUCTION DEPLOYMENT - EXCLUDING:"
          echo "❌ src/ directory - source code not needed"
          echo "❌ *.ts files (except medusa-config.ts) - already compiled"
          echo "❌ tsconfig.json - TypeScript config not needed"
          echo "❌ Development dependencies - not needed"
          echo "❌ Test files - not needed"
          echo "❌ Documentation files - not needed"

          cd ..
          echo "📋 Final deployment package contents:"
          ls -la deploy/

          echo "📋 .medusa/server contents (this is what runs in production):"
          ls -la deploy/.medusa/server/ || echo "❌ No .medusa/server in deployment"

          echo "📋 PRODUCTION DEPLOYMENT VERIFICATION:"
          echo "🔍 Checking for source files that shouldn't be in production..."

          # Check for source directories
          if find deploy/ -name "src" -type d | grep -q .; then
            echo "❌ ERROR: Source directories found in deployment!"
            find deploy/ -name "src" -type d
            exit 1
          else
            echo "✅ No source directories in deployment"
          fi

          # Check for TypeScript files (except medusa-config.ts)
          if find deploy/ -name "*.ts" -not -name "medusa-config.ts" -not -path "*/node_modules/*" | grep -q .; then
            echo "❌ ERROR: TypeScript source files found in deployment!"
            find deploy/ -name "*.ts" -not -name "medusa-config.ts" -not -path "*/node_modules/*"
            exit 1
          else
            echo "✅ No TypeScript source files in deployment (except medusa-config.ts)"
          fi

          # Check for development files
          if find deploy/ -name "tsconfig.json" -o -name "jest.config.js" -o -name "*.test.ts" -o -name "*.spec.ts" | grep -q .; then
            echo "❌ ERROR: Development files found in deployment!"
            find deploy/ -name "tsconfig.json" -o -name "jest.config.js" -o -name "*.test.ts" -o -name "*.spec.ts"
            exit 1
          else
            echo "✅ No development files in deployment"
          fi

          echo "📋 Deployment package size:"
          du -sh deploy/
          echo "📋 Breakdown by directory:"
          du -sh deploy/* 2>/dev/null || echo "No subdirectories to show"

          echo "📋 What's actually being deployed:"
          find deploy/ -type f | head -20

      - name: Debug deployment package before upload
        run: |
          echo "🔍 FINAL VERIFICATION BEFORE UPLOAD:"
          echo "📂 Current working directory: $(pwd)"
          echo "📂 Full path to current directory: $(realpath .)"
          echo "📁 All items in current directory:"
          ls -la
          echo "📁 Deploy directory exists: $(test -d deploy && echo 'YES' || echo 'NO')"
          echo "📁 ./deploy directory exists: $(test -d ./deploy && echo 'YES' || echo 'NO')"
          echo "📂 Full path to deploy directory: $(realpath ./deploy 2>/dev/null || echo 'DOES NOT EXIST')"

          if [ -d "deploy" ]; then
            echo "📋 Deploy directory contents (detailed):"
            ls -la deploy/

            echo "🔍 Checking for .medusa in deploy:"
            if [ -d "deploy/.medusa" ]; then
              echo "✅ .medusa directory found in deploy!"
              echo "📁 .medusa contents:"
              ls -la deploy/.medusa/
              if [ -d "deploy/.medusa/server" ]; then
                echo "✅ .medusa/server directory found!"
                echo "📁 .medusa/server contents:"
                ls -la deploy/.medusa/server/
              else
                echo "❌ No .medusa/server directory found"
              fi
            else
              echo "❌ No .medusa directory found in deploy package"
            fi

            echo "📊 Deploy package size breakdown:"
            du -sh deploy/* 2>/dev/null || echo "No files to measure"

            echo "📦 Total deploy package size:"
            du -sh deploy/
          else
            echo "❌ Deploy directory does not exist!"
            echo "📁 Current directory contents:"
            ls -la
          fi

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: ${{ github.workspace }}/deploy
          if-no-files-found: error
          include-hidden-files: true

  deploy:
    runs-on: ubuntu-latest
    needs: build
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Verify downloaded artifact
        run: |
          echo "🔍 Downloaded artifact contents:"
          ls -la
          echo "📁 Checking for .medusa directory:"
          ls -la .medusa/ || echo "❌ No .medusa directory in artifact"
          echo "📋 Total artifact size:"
          du -sh .
          echo "🔍 Node.js version in deployment:"
          node --version

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_3393D489ACA148AEA5AEBE7711560F91 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_F9EA7C3648F040778F81024500DA12B7 }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_BE9ED13D58B342E5A0D767CF12E0FB2C }}

      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'hebrew-book'
          slot-name: 'Production'
          package: '.'
          startup-command: 'chmod +x startup.sh && ./startup.sh'
