import { SubscriberArgs, SubscriberConfig } from "@medusajs/framework"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"

export default async function debugAllEventsHandler({
  event,
  container,
}: SubscriberArgs<any>) {
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER)

  // Log all events to see what's happening
  logger.info(`🔔 ALL EVENTS DEBUG: ${event.name}`)
  
  // Only log detailed info for invite/user related events
  if (event.name.includes('invite') || event.name.includes('user')) {
    logger.info(`📧 DETAILED EVENT: ${event.name}`)
    logger.info(`📧 Event data: ${JSON.stringify(event.data, null, 2)}`)
    logger.info(`📧 Event metadata: ${JSON.stringify(event.metadata || {}, null, 2)}`)
  }
}

export const config: SubscriberConfig = {
  event: "*", // Listen to all events
  context: {
    subscriberId: "debug-all-events-handler",
  },
}
