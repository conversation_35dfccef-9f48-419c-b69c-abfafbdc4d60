import { model } from "@medusajs/framework/utils"

const UserChapterAccess = model.define("user_chapter_access", {
  id: model.id().primaryKey(),
  user_id: model.text(),
  chapter_id: model.text(),
  access_type: model.text(), // "purchase", "subscription", "free"
  purchased_at: model.dateTime().nullable(),
  expires_at: model.dateTime().nullable(), // For subscription-based access
  payment_id: model.text().nullable(), // Reference to payment record
})

export default UserChapterAccess
