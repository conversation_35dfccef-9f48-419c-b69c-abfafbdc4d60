# 🧪 Hebrew Book Store - Testing Guide

## 🚀 Quick Start Testing

### 1. Start the Server
```bash
yarn dev
# Server runs on http://localhost:9000
```

### 2. Health Check
```bash
curl http://localhost:9000/health
# Should return: OK
```

---

## 🌐 Demo Pages Testing

### 🎯 **Main Demo (Multilingual) - RECOMMENDED**
**URL:** [http://localhost:9000/demo-v2](http://localhost:9000/demo-v2)

#### Test Scenarios:
1. **Language Switching:**
   - Click 🇺🇸 EN | 🇮🇱 עב | 🇷🇺 РУ buttons
   - Observe text direction change for Hebrew (RTL)
   - Check that preferences are saved (refresh page)

2. **Interactive Search:**
   - Type "משפחה" (family in Hebrew)
   - Type "alphabet" in English
   - Type "семья" (family in Russian)
   - Verify results appear in real-time

3. **Chapter Loading:**
   - Click "Load Chapters" button
   - Click "Free Only" button
   - Observe content changes with language

4. **Subscription Plans:**
   - Click "View Plans" button
   - Check pricing and features display
   - Verify localization in different languages

5. **Analytics:**
   - Click "View Analytics" button
   - Check statistics display
   - Verify data formatting

### 🛠️ **Admin Dashboard**
**URL:** [http://localhost:9000/admin-demo](http://localhost:9000/admin-demo)

#### Test Scenarios:
1. **Language Support:**
   - Switch languages using top-right switcher
   - Verify admin interface translates
   - Check RTL layout for Hebrew

2. **Dashboard Metrics:**
   - View quick stats (chapters, revenue, users)
   - Check analytics loading
   - Verify chapter management interface

3. **Tab Navigation:**
   - Test "Chapter Management" tab
   - Test "Analytics" tab
   - Test "Users" tab
   - Test "API Tests" tab

### 📊 **Classic Demo**
**URL:** [http://localhost:9000/demo](http://localhost:9000/demo)

#### Test Scenarios:
1. **API Testing:**
   - Test all API endpoint buttons
   - Verify JSON responses display
   - Check error handling

2. **Content Display:**
   - View Hebrew chapters
   - Test search functionality
   - Check subscription plans

---

## 📡 API Endpoints Testing

### Public APIs

#### Chapters
```bash
# List all chapters
curl http://localhost:9000/public/chapters

# Filter by difficulty
curl "http://localhost:9000/public/chapters?difficulty_level=beginner"

# Filter free chapters
curl "http://localhost:9000/public/chapters?is_free=true"

# Get specific chapter
curl http://localhost:9000/public/chapters/chapter_1
```

#### Search
```bash
# Search in Hebrew
curl "http://localhost:9000/public/search?q=משפחה"

# Search in English
curl "http://localhost:9000/public/search?q=alphabet"

# Search by type
curl "http://localhost:9000/public/search?q=colors&type=vocabulary"
```

#### Translations
```bash
# English translations
curl "http://localhost:9000/public/translations?language=en"

# Hebrew translations
curl "http://localhost:9000/public/translations?language=he"

# Russian translations
curl "http://localhost:9000/public/translations?language=ru"
```

#### Subscriptions & Stats
```bash
# Subscription plans
curl http://localhost:9000/public/subscriptions/plans

# Platform statistics
curl http://localhost:9000/public/stats
```

### Admin APIs (Note: May require authentication in production)
```bash
# Admin chapters list
curl http://localhost:9000/admin/chapters

# Admin analytics
curl http://localhost:9000/admin/analytics

# Specific chapter admin view
curl http://localhost:9000/admin/chapters/chapter_1
```

---

## 🎯 Feature Testing Checklist

### ✅ Multilingual Support
- [ ] Language switcher works on all pages
- [ ] Hebrew displays right-to-left (RTL)
- [ ] Russian Cyrillic characters display correctly
- [ ] Language preference persists after page reload
- [ ] Content translates appropriately

### ✅ Hebrew Content
- [ ] Hebrew text displays correctly
- [ ] RTL layout works properly
- [ ] Hebrew search functionality works
- [ ] Hebrew chapter content is readable
- [ ] Hebrew tags and metadata display

### ✅ E-commerce Features
- [ ] Chapter pricing displays correctly
- [ ] Free vs paid content access control
- [ ] Subscription plans show proper pricing
- [ ] Different subscription durations available
- [ ] Payment integration setup (Stripe webhooks)

### ✅ Search Functionality
- [ ] Search works in Hebrew
- [ ] Search works in English
- [ ] Search works in Russian
- [ ] Results show relevance scores
- [ ] Search suggestions appear
- [ ] No results message displays appropriately

### ✅ Analytics & Admin
- [ ] Admin dashboard loads
- [ ] Statistics display correctly
- [ ] Chapter management interface works
- [ ] Analytics show real data
- [ ] Geographic distribution displays
- [ ] Revenue metrics calculate properly

### ✅ Performance & UX
- [ ] Pages load quickly
- [ ] Language switching is smooth
- [ ] No console errors
- [ ] Responsive design works
- [ ] Animations are smooth
- [ ] API responses are fast

---

## 🐛 Common Issues & Solutions

### Issue: Hebrew text not displaying correctly
**Solution:** Ensure browser supports Hebrew fonts, check CSS direction property

### Issue: Language switcher not working
**Solution:** Check JavaScript console for errors, verify localStorage support

### Issue: API endpoints returning 404
**Solution:** Ensure server is running on port 9000, check endpoint URLs

### Issue: RTL layout broken
**Solution:** Verify CSS RTL classes are applied, check direction attribute

### Issue: Search not working
**Solution:** Check search input has content, verify API endpoint response

---

## 📊 Expected Test Results

### Successful API Responses
- **Chapters API:** Returns 3 Hebrew chapters with multilingual fields
- **Search API:** Returns relevant results with scores
- **Translations API:** Returns complete translation dictionaries
- **Stats API:** Returns platform statistics and metrics

### Successful UI Behavior
- **Language Switching:** Instant UI translation without page reload
- **RTL Support:** Hebrew content displays right-to-left
- **Content Loading:** Dynamic content updates based on language
- **Responsive Design:** Works on desktop, tablet, and mobile

### Performance Expectations
- **Page Load:** < 2 seconds
- **Language Switch:** < 500ms
- **API Response:** < 1 second
- **Search Results:** < 800ms

---

## 🎉 Success Criteria

### ✅ All Tests Pass
- All demo pages load and function correctly
- All API endpoints return expected data
- Language switching works smoothly
- Hebrew RTL support functions properly
- Search functionality works in all languages
- Admin dashboard displays analytics correctly

### ✅ User Experience
- Intuitive language switching
- Smooth transitions and animations
- Responsive design on all devices
- Clear visual feedback for user actions
- Professional appearance and layout

### ✅ Technical Performance
- Fast loading times
- No JavaScript errors
- Proper error handling
- Efficient API responses
- Clean, maintainable code structure

---

**🌟 Ready for Production Testing! 🌟**

*The Hebrew Book Store platform is fully functional and ready for real-world use.*
