# Hebrew Book Store Frontend Environment Variables

# Backend API URL
VITE_BACKEND_URL=http://localhost:9000

# Medusa.js Publishable API Key
# Create this key in Medusa Admin Panel: http://localhost:9000/app
# Go to Settings → API Keys → Create API Key → Type: Publishable
VITE_MEDUSA_PUBLISHABLE_API_KEY=pk_your_publishable_key_here

# Default Region ID for pricing calculations
# Get this from Medusa Admin Panel: http://localhost:9000/app
# Go to Settings → Regions → Copy the region ID (e.g., reg_01ABC123...)
# This determines the currency and pricing context for products
VITE_DEFAULT_REGION_ID=your_region_id_here

# App Configuration
VITE_APP_NAME=Hebrew Book Store
VITE_APP_URL=http://localhost:5173

# Development/Production Mode
NODE_ENV=development
