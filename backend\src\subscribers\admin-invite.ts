import { SubscriberArgs, SubscriberConfig } from "@medusajs/framework"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import NotificationService from "../modules/notification/service"

export default async function adminInviteHandler({
  event,
  container,
}: SubscriberArgs<any>) {
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER)

  logger.info(`🔔 SUBSCRIBER CALLED: ${event.name}`)

  // Only handle invite.created events
  if (event.name === "invite.created" && event.data?.id) {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    const notificationService: NotificationService = container.resolve("notificationService")

    try {
      logger.info(`📧 Fetching invite details for ID: ${event.data.id}`)

      // Query the invite from database to get email and token
      const { data: invites } = await query.graph({
        entity: "invite",
        fields: ["id", "email", "token", "expires_at", "accepted", "created_at"],
        filters: { id: event.data.id }
      })

      if (invites && invites.length > 0) {
        const invite = invites[0]
        logger.info(`📧 Found invite: ${JSON.stringify(invite, null, 2)}`)

        if (invite.email && invite.token) {
          logger.info(`📧 Sending admin invite email to: ${invite.email}`)

          const result = await notificationService.sendAdminInvite(
            invite.email,
            invite.token
          )

          if (result.success) {
            logger.info(`✅ Admin invite email sent successfully to: ${invite.email}`)
          } else {
            logger.error(`❌ Failed to send admin invite email to: ${invite.email}`, result.error)
          }
        } else {
          logger.warn(`❌ Invite missing email or token: ${JSON.stringify(invite)}`)
        }
      } else {
        logger.warn(`❌ No invite found with ID: ${event.data.id}`)
      }
    } catch (error) {
      logger.error(`❌ Error in invite handler:`, error)
    }
  } else {
    logger.info(`📧 Ignoring event: ${event.name}`)
  }
}

export const config: SubscriberConfig = {
  event: "invite.created",
  context: {
    subscriberId: "admin-invite-handler",
  },
}
