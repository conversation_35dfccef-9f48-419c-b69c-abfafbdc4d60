import { writable } from 'svelte/store'
import type { AdminUser } from '$lib/api/admin'

// Admin authentication store
export const adminStore = writable<{
  isAuthenticated: boolean
  currentAdmin: AdminUser | null
  isLoading: boolean
}>({
  isAuthenticated: false,
  currentAdmin: null,
  isLoading: false
})

// Simple demo authentication
export function loginAdmin(email: string, password: string): boolean {
  // Demo credentials
  const validCredentials = [
    { email: '<EMAIL>', password: 'admin123', role: 'admin' },
    { email: '<EMAIL>', password: 'editor123', role: 'editor' }
  ]

  const user = validCredentials.find(u => u.email === email && u.password === password)
  
  if (user) {
    const adminUser: AdminUser = {
      id: user.role === 'admin' ? 'admin_1' : 'editor_1',
      email: user.email,
      name: user.role === 'admin' ? 'Demo Admin' : 'Demo Editor',
      role: user.role,
      permissions: user.role === 'admin' 
        ? ['chapters:read', 'chapters:write', 'analytics:read', 'users:read']
        : ['chapters:read', 'chapters:write']
    }

    adminStore.set({
      isAuthenticated: true,
      currentAdmin: adminUser,
      isLoading: false
    })

    // Save to localStorage for persistence
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('admin_session', JSON.stringify(adminUser))
    }

    return true
  }

  return false
}

export function logoutAdmin() {
  adminStore.set({
    isAuthenticated: false,
    currentAdmin: null,
    isLoading: false
  })

  // Clear localStorage
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('admin_session')
  }
}

export function checkAdminSession() {
  if (typeof localStorage !== 'undefined') {
    const session = localStorage.getItem('admin_session')
    if (session) {
      try {
        const adminUser = JSON.parse(session) as AdminUser
        adminStore.set({
          isAuthenticated: true,
          currentAdmin: adminUser,
          isLoading: false
        })
        return true
      } catch (e) {
        localStorage.removeItem('admin_session')
      }
    }
  }
  return false
}
