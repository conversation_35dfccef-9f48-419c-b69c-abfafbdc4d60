<script lang="ts">
  import { onMount } from 'svelte'
  import { page } from '$app/stores'
  import { _ } from '$lib/i18n'
  import { goto } from '$app/navigation'
  import { cartActions } from '$lib/stores/cart'

  let countdown = 5

  onMount(() => {
    // Initialize cart to ensure it's properly reset after order completion
    cartActions.initialize().then(() => {
      console.log('✅ Cart reinitialized on success page')
    })

    // Start countdown to redirect to library
    const interval = setInterval(() => {
      countdown--
      if (countdown <= 0) {
        clearInterval(interval)
        goto('/library')
      }
    }, 1000)

    return () => clearInterval(interval)
  })

  function goToLibrary() {
    goto('/library')
  }

  function goToOrders() {
    goto('/orders')
  }

  function goHome() {
    goto('/')
  }
</script>

<svelte:head>
  <title>{$_('checkout.order_success')} - Hebrew Book Store</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center py-8">
  <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
      <!-- Success Header -->
      <div class="bg-gradient-to-r from-green-500 to-green-600 px-8 py-12 text-center">
        <div class="text-white text-8xl mb-6">🎉</div>
        <h1 class="text-4xl font-bold text-white mb-4">
          {$_('checkout.payment_successful')}
        </h1>
        <p class="text-green-100 text-lg">
          {$_('checkout.order_completed_successfully')}
        </p>
      </div>

      <!-- Content -->
      <div class="px-8 py-8">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">
            {$_('checkout.what_happens_next')}
          </h2>
          <div class="space-y-4 text-left max-w-md mx-auto">
            <div class="flex items-start">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                <span class="text-green-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h3 class="font-medium text-gray-800">{$_('checkout.order_processed')}</h3>
                <p class="text-gray-600 text-sm">{$_('checkout.order_processed_description')}</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                <span class="text-green-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h3 class="font-medium text-gray-800">{$_('checkout.books_available')}</h3>
                <p class="text-gray-600 text-sm">{$_('checkout.books_available_description')}</p>
              </div>
            </div>
            
            <div class="flex items-start">
              <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                <span class="text-green-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h3 class="font-medium text-gray-800">{$_('checkout.start_reading')}</h3>
                <p class="text-gray-600 text-sm">{$_('checkout.start_reading_description')}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Auto-redirect notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-center">
            <div class="text-blue-500 mr-2">📚</div>
            <p class="text-blue-800 text-sm">
              {$_('checkout.auto_redirect_library')} <span class="font-semibold">{countdown}</span> {$_('checkout.seconds')}
            </p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            on:click={goToLibrary}
            class="btn-primary flex items-center justify-center"
          >
            📚 {$_('checkout.go_to_library')}
          </button>
          
          <button
            on:click={goToOrders}
            class="btn-secondary flex items-center justify-center"
          >
            📋 {$_('checkout.view_orders')}
          </button>
          
          <button
            on:click={goHome}
            class="btn-outline flex items-center justify-center"
          >
            🏠 {$_('checkout.back_to_home')}
          </button>
        </div>

        <!-- Additional Info -->
        <div class="mt-8 pt-6 border-t border-gray-200 text-center">
          <p class="text-gray-600 text-sm mb-2">
            {$_('checkout.need_help')}
          </p>
          <div class="flex justify-center space-x-4 text-sm">
            <a href="/contact" class="text-blue-600 hover:text-blue-800">
              {$_('checkout.contact_support')}
            </a>
            <span class="text-gray-400">•</span>
            <a href="/orders" class="text-blue-600 hover:text-blue-800">
              {$_('checkout.order_history')}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .btn-primary {
    @apply px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors;
  }

  .btn-outline {
    @apply px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors;
  }
</style>
